# Datasource 插件概要设计文档

## 1. 插件概述

Datasource 插件是一个为 Unreal Engine 开发的数据交换插件，主要用于在 UE 和 Android/Linux 平台之间建立双向通信通道。该插件采用发布-订阅模式，允许 UE 蓝图和 C++ 代码与外部平台进行数据交互，特别适用于车载系统开发场景。

## 2. 设计目标

- 提供简单易用的蓝图接口，方便非程序员使用
- 支持多种数据类型的传输（整数、浮点数、字符串、数组、向量等）
- 实现 UE 和 Android/Linux 平台之间的双向通信
- 支持多个订阅者同时接收消息
- 提供高效的数据存储和分发机制

## 3. 系统架构

### 3.1 核心组件

插件由以下核心组件构成：

1. **UDatasource**：蓝图函数库，提供蓝图可调用的接口
2. **UDatasourceImpl**：实现类，负责具体功能实现
3. **Publisher**：发布者，负责消息的发布和管理
4. **ASubscriber**：订阅者，负责接收和处理消息
5. **AndroidDataFeeder**：Android 平台数据交互接口

### 3.2 架构图

```
+----------------+      +------------------+      +----------------+
|   UDatasource  |----->| UDatasourceImpl  |----->|   Publisher    |
| (蓝图函数库)    |      | (具体实现类)      |      | (消息发布管理) |
+----------------+      +------------------+      +----------------+
                                |                        |
                                v                        v
                        +------------------+      +----------------+
                        |AndroidDataFeeder |<---->|  ASubscriber  |
                        | (Android接口)    |      | (消息订阅处理) |
                        +------------------+      +----------------+
```

## 4. 功能概述

### 4.1 数据源管理

- 创建和启动数据源
- 停止数据源
- 注册订阅者

### 4.2 数据传输

- 支持多种数据类型：整数、浮点数、字符串、数组、向量等
- UE 到 Android/Linux 的数据发送
- Android/Linux 到 UE 的数据接收

### 4.3 订阅管理

- 添加订阅主题
- 移除订阅主题
- 基于文件批量添加订阅主题

### 4.4 事件处理

- 提供蓝图可实现的事件接口
- 支持多种车辆事件类型的处理

## 5. 数据流程

### 5.1 UE 到 Android/Linux

1. 蓝图或 C++ 代码调用 UDatasource 的接口
2. 数据通过 UDatasourceImpl 传递给 Publisher
3. 在设备上运行时，数据通过 JNI 发送给 Android

### 5.2 Android/Linux 到 UE

1. Android 通过 JNI 发送数据
2. 数据通过 AndroidDataFeeder 传递给 UDatasourceImpl
3. UDatasourceImpl 将数据传递给 Publisher
4. Publisher 将数据分发给订阅者
5. 订阅者处理数据并触发相应事件

## 6. 设计模式

### 6.1 单例模式

Publisher 采用单例模式，确保全局只有一个发布者实例。

```cpp
static Publisher* GetInstance() {
    if (nullptr == sPubInst) {
        sPubInst = new Publisher();
        sPubInst->mThreadRunning = 1;
    }
    return sPubInst;
}
```

### 6.2 发布-订阅模式

整个插件基于发布-订阅模式设计，Publisher 负责发布消息，ASubscriber 负责订阅和处理消息。

### 6.3 工厂模式

UDatasource 的 CreateAndStartDatasource 方法采用工厂模式创建 UDatasourceImpl 实例。

```cpp
UDatasourceImpl* UDatasource::CreateAndStartDatasource() {
    if (!sDatasourceImpl) {
        sDatasourceImpl = NewObject<UDatasourceImpl>();
        sDatasourceImpl->AddToRoot();
        sDatasourceImpl->InitPublisher();
        // ...
    }
    return sDatasourceImpl;
}
```

## 7. 接口设计

### 7.1 蓝图接口

UDatasource 提供了以下主要蓝图接口：

- `CreateAndStartDatasource()`：创建并启动数据源
- `StopDatasource()`：停止数据源
- `RegisterSubscriber()`：注册订阅者
- `NotifyPlatformXXX()`：向平台发送数据
- `SetXXXValue()`：设置数据值

### 7.2 订阅者接口

ASubscriber 提供了以下主要接口：

- `AddTopic()`：添加订阅主题
- `AddTopics()`：添加多个订阅主题
- `RemoveTopic()`：移除订阅主题
- `RemoveAllTopic()`：移除所有订阅主题
- `XXXValueUpdateEvent()`：值更新事件

## 8. 数据类型支持

插件支持以下数据类型：

- 整数 (int)
- 浮点数 (float)
- 字符串 (FString/std::string)
- 浮点数数组 (TArray<float>)
- 向量数组 (TArray<FVector>)
- 向量映射 (TMap<int, FVector>)

## 9. 平台支持

- UE 编辑器模式
- Android 设备
- 可扩展支持其他平台

## 10. 使用场景

- 车载系统开发
- 多平台数据交互
- 实时数据监控和控制
- 车辆状态模拟和测试
