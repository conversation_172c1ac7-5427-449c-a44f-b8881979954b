# 开发者个人能力栈思维导图

```mermaid
mindmap
  root((开发者能力栈))
    (UE4开发)
      [核心概念]
        (蓝图系统)
        (C++与蓝图交互)
        (Actor组件系统)
        (委托与事件系统)
        (Gameplay框架)
      [图形渲染]
        (材质系统)
        (后期处理)
        (光照与阴影)
        (着色器编程)
        (LOD系统)
      [动画系统]
        (骨骼动画)
        (动画蓝图)
        (状态机)
      [UI开发]
        (UMG系统)
        (Slate)
      [性能优化]
        (内存管理)
        (渲染优化)
        (资产优化)
      [工具开发]
        (编辑器扩展)
        (自定义工具)
    (设计模式)
      [创建型模式]
        (单例模式)
        (工厂模式)
        (建造者模式)
        (原型模式)
      [结构型模式]
        (适配器模式)
        (装饰器模式)
        (代理模式)
        (组合模式)
        (外观模式)
      [行为型模式]
        (观察者模式)
        (策略模式)
        (命令模式)
        (状态模式)
        (模板方法模式)
      [游戏特有模式]
        (组件模式)
        (对象池模式)
        (服务定位器)
        (实体组件系统)
    (编程语言)
      [C++]
        (现代C++特性)
        (内存管理)
        (多线程编程)
        (模板元编程)
      [Python]
        (脚本自动化)
        (数据处理)
        (工具开发)
      [蓝图可视化编程]
        (事件图表)
        (宏与函数)
        (接口实现)
    (开发工具)
      [IDE与编辑器]
        (Visual Studio)
        (Visual Studio Code)
        (Rider)
      [构建工具]
        (Unreal Build Tool)
      [持续集成]
        (GitLab CI)
      [项目管理]
        (Jira)
        (禅道)
        (Redmine)
    (版本控制)
      [Git]
        (基本操作)
          (commit)
          (branch)
          (merge)
          (rebase)
        (工作流)
          (Git Flow)
          (GitHub Flow)
          (GitLab Flow)
        (协作技巧)
          (Pull Request)
          (Code Review)
          (冲突解决)
        (高级功能)
          (子模块)
          (LFS)
    (软技能)
      [团队协作]
        (沟通能力)
        (冲突解决)
        (知识分享)
      [项目管理]
        (需求分析)
        (任务分解)
        (风险管理)
        (进度跟踪)
      [自我管理]
        (时间管理)
        (持续学习)
        (压力管理)
```

## 能力栈详细说明

### UE4开发

#### 核心概念
- **蓝图系统**：可视化编程接口的熟练程度，自定义节点开发
- **C++与蓝图交互**：UFUNCTION、UPROPERTY暴露，蓝图可调用函数
- **Actor组件系统**：组件化设计理解与应用
- **委托与事件系统**：多播委托、动态委托、事件分发
- **Gameplay框架**：GameMode、PlayerController、Pawn等类的应用

#### 图形渲染
- **材质系统**：PBR工作流，材质函数，材质实例
- **后期处理**：后期处理体积，自定义后期处理
- **光照与阴影**：静态/动态光照，光照构建，全局光照
- **着色器编程**：自定义HLSL着色器，材质表达式
- **LOD系统**：模型LOD，HLOD系统

### 设计模式

#### 创建型模式
- **单例模式**：全局访问点，如游戏实例、存档管理器
- **工厂模式**：对象创建抽象，如敌人生成系统
- **建造者模式**：复杂对象构建，如角色创建器
- **原型模式**：对象克隆，如技能复制系统

#### 结构型模式
- **适配器模式**：接口转换，如第三方库集成
- **装饰器模式**：动态添加功能，如武器升级系统
- **组合模式**：部分-整体层次结构，如UI组件树
- **外观模式**：子系统统一接口，如游戏管理器

### 开发工具

#### IDE与编辑器
- **Visual Studio**：C++开发环境，调试技能
- **Rider**：现代C++/C#IDE，重构工具
- **Visual Studio Code**：轻量级编辑，扩展生态

#### 调试工具
- **调试器使用**：断点，条件断点，内存检查
- **性能分析**：CPU/GPU分析器，热点识别
- **内存分析**：内存泄漏检测，内存使用优化

### 版本控制

#### Git
- **基本操作**：日常提交，分支管理，合并策略
- **工作流**：团队协作模式，分支策略
- **高级功能**：子模块管理，大文件存储

#### Perforce
- **基本操作**：检出/签入，变更列表
- **与UE4集成**：引擎内资产版本控制

### 游戏数学
- **线性代数**：向量/矩阵运算，空间变换
- **几何数学**：碰撞检测算法，空间查询
- **物理数学**：运动方程，约束求解

### 软技能
- **团队协作**：有效沟通，代码审查，知识共享
- **项目管理**：任务分解，风险评估，进度跟踪
- **自我管理**：时间规划，持续学习，解决问题
