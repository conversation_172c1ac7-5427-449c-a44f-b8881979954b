@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    简化版 Jira 连接测试
echo ========================================
echo.

:: 设置基本配置（避免复杂的文件读取）
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"
set "API_TOKEN=NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762"
set "SIMPLE_JQL=assignee = currentUser()"

echo 配置信息:
echo JIRA_URL: %JIRA_URL%
echo USERNAME: %USERNAME%
echo API_TOKEN: %API_TOKEN:~0,10%...
echo JQL查询: %SIMPLE_JQL%
echo.

:: 测试1: 基本连接
echo [测试1] 基本连接测试
echo 测试URL: %JIRA_URL%
curl -s --connect-timeout 10 --max-time 30 "%JIRA_URL%" -o nul
set "BASIC_CODE=!errorlevel!"
echo 基本连接退出代码: !BASIC_CODE!

if !BASIC_CODE! equ 0 (
    echo 基本连接: 成功
) else (
    echo 基本连接: 失败
    if !BASIC_CODE! equ 6 echo   - 错误6: 无法解析主机名
    if !BASIC_CODE! equ 7 echo   - 错误7: 无法连接到服务器
    if !BASIC_CODE! equ 3 echo   - 错误3: URL格式错误
)
echo.

:: 测试2: serverInfo API
echo [测试2] serverInfo API测试
set "SERVER_URL=%JIRA_URL%/rest/api/2/serverInfo"
echo 测试URL: %SERVER_URL%
curl -s --connect-timeout 10 --max-time 30 "%SERVER_URL%" -o nul
set "SERVER_CODE=!errorlevel!"
echo serverInfo退出代码: !SERVER_CODE!

if !SERVER_CODE! equ 0 (
    echo serverInfo API: 成功
) else (
    echo serverInfo API: 失败
)
echo.

:: 测试3: 认证测试
echo [测试3] 认证测试
set "AUTH_URL=%JIRA_URL%/rest/api/2/myself"
echo 测试URL: %AUTH_URL%
curl -s -u "%USERNAME%:%API_TOKEN%" --connect-timeout 10 --max-time 30 "%AUTH_URL%" -o nul
set "AUTH_CODE=!errorlevel!"
echo 认证退出代码: !AUTH_CODE!

if !AUTH_CODE! equ 0 (
    echo 认证: 成功
) else (
    echo 认证: 失败
    if !AUTH_CODE! equ 22 echo   - 错误22: HTTP错误（可能是401未授权）
)
echo.

:: 测试4: 简单搜索
if !AUTH_CODE! equ 0 (
    echo [测试4] 简单搜索测试
    set "SEARCH_URL=%JIRA_URL%/rest/api/2/search"
    echo 测试URL: %SEARCH_URL%
    echo JQL查询: %SIMPLE_JQL%
    
    curl -s -u "%USERNAME%:%API_TOKEN%" -H "Accept: application/json" -G "%SEARCH_URL%" --data-urlencode "jql=%SIMPLE_JQL%" --data-urlencode "maxResults=1" --connect-timeout 10 --max-time 30 -o nul
    set "SEARCH_CODE=!errorlevel!"
    echo 搜索退出代码: !SEARCH_CODE!
    
    if !SEARCH_CODE! equ 0 (
        echo 搜索: 成功
        echo.
        echo ========================================
        echo   所有测试通过！
        echo   Jira连接正常，可以使用主脚本
        echo ========================================
    ) else (
        echo 搜索: 失败
    )
) else (
    echo [跳过] 由于认证失败，跳过搜索测试
)

echo.

:: 如果基本连接失败，提供替代方案
if !BASIC_CODE! neq 0 (
    echo ========================================
    echo   连接失败，尝试替代方案
    echo ========================================
    echo.
    
    echo 尝试HTTP版本...
    set "HTTP_URL=http://globaljira.geely.com"
    curl -s --connect-timeout 5 --max-time 10 "%HTTP_URL%" -o nul
    set "HTTP_CODE=!errorlevel!"
    if !HTTP_CODE! equ 0 (
        echo HTTP连接成功！请使用: %HTTP_URL%
    ) else (
        echo HTTP连接也失败
    )
    
    echo.
    echo 尝试带端口的版本...
    set "PORT_URL=http://globaljira.geely.com:8080"
    curl -s --connect-timeout 5 --max-time 10 "%PORT_URL%" -o nul
    set "PORT_CODE=!errorlevel!"
    if !PORT_CODE! equ 0 (
        echo 带端口连接成功！请使用: %PORT_URL%
    ) else (
        echo 带端口连接也失败
    )
    
    echo.
    echo 建议:
    echo 1. 检查VPN连接
    echo 2. 确认网络连接
    echo 3. 联系IT部门确认正确的Jira地址
)

echo.
pause
