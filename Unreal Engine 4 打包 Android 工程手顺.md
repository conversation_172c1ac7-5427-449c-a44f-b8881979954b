# Unreal Engine 4 打包 Android 工程手顺

## 1. 环境准备

### 1.1 下载和安装必要的软件

- **Unreal Engine 4**: [下载链接](https://www.unrealengine.com/en-US/download)
- **Android Studio**: [下载链接](https://developer.android.com/studio)
- **Java Development Kit (JDK)**: [下载链接](https://www.oracle.com/java/technologies/javase-jdk11-downloads.html)（建议使用 JDK 8 或 11）

### 1.2 安装 Android SDK 和 NDK

1. **安装 Android Studio**:
   - 下载并安装 Android Studio，安装过程中选择默认选项。
   - 启动 Android Studio，选择 **Configure** -> **SDK Manager**。

2. **配置 Android SDK**:
   - 在 SDK Manager 中，确保安装以下组件：
     - **Android SDK Platform-Tools**
     - **Android SDK Build-Tools**（建议安装最新版本）
     - **Android SDK Tools**
     - **Android 21 (或更高版本)**（根据你的项目需求选择合适的 API 级别）

3. **安装 Android NDK**:
   - 在 SDK Manager 中，切换到 **SDK Tools** 标签。
   - 勾选 **NDK (Side by side)**，然后点击 **OK** 进行安装。

### 1.3 配置环境变量

在你的操作系统中设置以下环境变量：

- **JAVA_HOME**: 指向 JDK 安装目录，例如 `C:\Program Files\Java\jdk1.8.0_251`
- **ANDROID_HOME**: 指向 Android SDK 安装目录，例如 `C:\Users\<USER>\AppData\Local\Android\Sdk`
- **Path**: 在 Path 中添加以下路径：
  - `%ANDROID_HOME%\platform-tools`
  - `%ANDROID_HOME%\tools`
  - `%JAVA_HOME%\bin`

## 2. Unreal Engine 4 配置

### 2.1 启用 Android 支持

1. 打开 Unreal Engine 4。
2. 在启动器中，选择你的项目。
3. 进入 **Edit** -> **Plugins**。
4. 在插件列表中，找到 **Android**，并启用它。

### 2.2 配置项目设置

1. 进入 **Edit** -> **Project Settings**。
2. 在左侧菜单中选择 **Platforms** -> **Android**。
3. 配置以下选项：
   - **SDK Configurations**:
     - **SDK Path**: 指向 Android SDK 的安装目录，例如 `C:\Users\<USER>\AppData\Local\Android\Sdk`。
     - **NDK Path**: 指向 Android NDK 的安装目录，例如 `C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\21.3.6528147`（根据你的 NDK 版本调整）。
   - **Package Name**: 设置你的包名，例如 `com.yourcompany.yourproject`。
   - **Minimum SDK Version**: 设置为 `API 21` 或更高。
   - **Target SDK Version**: 设置为最新的 API 级别（例如 `API 30`）。
   - **Build Configuration**: 选择 `Development` 或 `Shipping`。

## 3. 打包项目

1. 在主菜单中，选择 **File** -> **Package Project** -> **Android**。
2. 选择 **Android (ETC2)** 或其他适合的选项。
3. 选择输出目录，点击 **OK** 开始打包。

## 4. 部署到设备

1. 确保你的 Android 设备已启用 USB 调试。
2. 将设备连接到电脑。
3. 在 Unreal Engine 中，选择 **File** -> **Deploy to Android**。

## 5. 常见问题

- **构建失败**: 确保所有环境变量正确设置，并且 SDK 和 NDK 版本兼容。
- **设备未识别**: 确保 USB 调试已启用，并且驱动程序已安装。

## 6. 参考链接

- [Unreal Engine Android Documentation](https://docs.unrealengine.com/en-US/Platforms/Android/index.html)
- [Android Studio Documentation](https://developer.android.com/studio/)
