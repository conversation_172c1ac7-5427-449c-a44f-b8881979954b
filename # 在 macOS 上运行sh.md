# 在 macOS 上运行 .sh 脚本的指南

## 1. 准备工作

在开始之前，请确保您已经安装了 macOS，并且可以访问终端（Terminal）。您可以通过以下步骤打开终端：

- 点击屏幕左上角的苹果图标（Apple Menu）。
- 选择“应用程序”（Applications）。
- 找到并打开“实用工具”（Utilities）文件夹。
- 双击“终端”（Terminal）图标。

## 2. 创建或获取 .sh 脚本

您可以使用文本编辑器（如 TextEdit、Visual Studio Code 或 Sublime Text）创建一个新的 `.sh` 文件，或者获取现有的脚本文件。

### 示例脚本

以下是一个示例脚本，您可以将其复制到文本编辑器中并保存为 `example.sh`：

```bash
bash
#!/bin/bash
echo "Hello, World!"
```

## 3. 保存脚本文件

确保将文件保存为纯文本格式，并以 `.sh` 作为文件扩展名。例如，您可以将其命名为 `example.sh`。

## 4. 打开终端

如前所述，打开终端应用程序。

## 5. 导航到脚本所在的目录

使用 `cd` 命令导航到包含 `.sh` 脚本的目录。例如，如果您的脚本保存在桌面上，可以输入：

```bash
bash cd ~/Desktop
```

## 6. 赋予脚本执行权限

在运行脚本之前，您需要确保它具有执行权限。使用以下命令为脚本赋予执行权限：

```bash
bash chmod +x example.sh
```


## 7. 运行脚本

现在，您可以运行脚本。使用以下命令：

```bash
bash ./example.sh
```

如果一切正常，您应该会看到输出：

```bash
Hello, World!
```

## 8. 常见问题

### 8.1 脚本没有执行权限

如果您在运行脚本时遇到“权限被拒绝”的错误，请确保您已正确使用 `chmod +x` 命令为脚本赋予执行权限。

### 8.2 找不到命令

如果您在运行脚本时遇到“找不到命令”的错误，请确保您在终端中导航到正确的目录，并且脚本文件名拼写正确。

## 9. 结论

通过以上步骤，您应该能够在 macOS 上成功运行 `.sh` 脚本。如果您有任何问题或需要进一步的帮助，请随时查阅相关文档或在线资源。