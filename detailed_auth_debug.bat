@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    详细认证调试工具 v2
echo ========================================
echo.

set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

echo 请输入您的Jira密码:
set /p PASSWORD=

if "!PASSWORD!"=="" (
    echo 密码不能为空！
    pause
    exit /b 1
)

echo.
echo 开始详细测试...
echo.

:: 测试1: 基本认证到myself端点
echo [测试1] 基本认证测试 - myself端点
echo URL: !JIRA_URL!/rest/api/2/myself
echo.
curl -v -u "!USERNAME!:!PASSWORD!" "!JIRA_URL!/rest/api/2/myself" 2>&1
echo.
echo ========================================
echo.

:: 测试2: 添加Accept头部
echo [测试2] 添加Accept头部
curl -v -u "!USERNAME!:!PASSWORD!" -H "Accept: application/json" "!JIRA_URL!/rest/api/2/myself" 2>&1
echo.
echo ========================================
echo.

:: 测试3: 添加更多头部
echo [测试3] 添加更多认证头部
curl -v -u "!USERNAME!:!PASSWORD!" ^
     -H "Accept: application/json" ^
     -H "Content-Type: application/json" ^
     -H "X-Atlassian-Token: no-check" ^
     "!JIRA_URL!/rest/api/2/myself" 2>&1
echo.
echo ========================================
echo.

:: 测试4: 测试搜索端点
echo [测试4] 测试搜索端点 - 无JQL
echo URL: !JIRA_URL!/rest/api/2/search?maxResults=1
curl -v -u "!USERNAME!:!PASSWORD!" -H "Accept: application/json" "!JIRA_URL!/rest/api/2/search?maxResults=1" 2>&1
echo.
echo ========================================
echo.

:: 测试5: 使用URL编码的认证
echo [测试5] URL编码认证测试
set "ENCODED_USER=!USERNAME!"
set "ENCODED_PASS=!PASSWORD!"
curl -v --user "!ENCODED_USER!:!ENCODED_PASS!" -H "Accept: application/json" "!JIRA_URL!/rest/api/2/myself" 2>&1
echo.
echo ========================================
echo.

:: 测试6: 检查是否需要会话认证
echo [测试6] 检查会话认证端点
echo URL: !JIRA_URL!/rest/auth/1/session
curl -v -X POST -H "Content-Type: application/json" -d "{\"username\":\"!USERNAME!\",\"password\":\"!PASSWORD!\"}" "!JIRA_URL!/rest/auth/1/session" 2>&1
echo.
echo ========================================
echo.

:: 测试7: 检查API版本
echo [测试7] 检查不同API版本
echo 测试 /rest/api/latest/myself
curl -v -u "!USERNAME!:!PASSWORD!" -H "Accept: application/json" "!JIRA_URL!/rest/api/latest/myself" 2>&1
echo.
echo ========================================

echo.
echo 测试完成！
echo.
echo 分析说明:
echo - 查看HTTP状态码 (200=成功, 401=认证失败, 403=权限不足)
echo - 查看响应头部中的认证要求
echo - 查看是否有重定向 (301/302)
echo - 查看错误消息中的具体原因

pause
