@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    会话认证测试
echo ========================================
echo.

set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

echo 请输入您的Jira密码:
set /p PASSWORD=

echo.
echo 尝试会话认证方式...
echo.

:: 步骤1: 创建会话
echo [步骤1] 创建Jira会话
set "SESSION_FILE=%TEMP%\jira_session_%RANDOM%.json"
set "COOKIE_FILE=%TEMP%\jira_cookies_%RANDOM%.txt"

curl -v -X POST ^
     -H "Content-Type: application/json" ^
     -d "{\"username\":\"!USERNAME!\",\"password\":\"!PASSWORD!\"}" ^
     -c "!COOKIE_FILE!" ^
     -o "!SESSION_FILE!" ^
     "!JIRA_URL!/rest/auth/1/session" 2>&1

echo.
echo 会话创建响应:
if exist "!SESSION_FILE!" (
    type "!SESSION_FILE!"
    echo.
)

:: 步骤2: 使用会话cookie进行API调用
echo [步骤2] 使用会话cookie调用API
if exist "!COOKIE_FILE!" (
    echo 使用cookie文件: !COOKIE_FILE!
    
    curl -v -b "!COOKIE_FILE!" ^
         -H "Accept: application/json" ^
         "!JIRA_URL!/rest/api/2/myself" 2>&1
    
    echo.
    echo ========================================
    echo.
    
    echo [步骤3] 使用会话cookie进行搜索
    curl -v -b "!COOKIE_FILE!" ^
         -H "Accept: application/json" ^
         "!JIRA_URL!/rest/api/2/search?jql=assignee=currentUser()&maxResults=1" 2>&1
) else (
    echo 未生成cookie文件，会话创建可能失败
)

:: 清理临时文件
if exist "!SESSION_FILE!" del "!SESSION_FILE!"
if exist "!COOKIE_FILE!" del "!COOKIE_FILE!"

echo.
echo ========================================
echo 会话认证测试完成
echo.
echo 如果会话认证成功，我们可以修改主脚本使用这种方式
echo ========================================

pause
