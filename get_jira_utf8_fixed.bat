@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: Jira问题剩余数量获取脚本 (UTF-8修复版)
:: 作者: Assistant
:: 版本: 2.1
:: 描述: 修复中文乱码问题
:: ========================================

echo.
echo ========================================
echo    Jira 问题剩余数量获取工具 (UTF-8版)
echo ========================================
echo.

:: 配置信息
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

:: 安全地获取密码
echo 请输入您的Jira密码:
set /p PASSWORD=

if "!PASSWORD!"=="" (
    echo [错误] 密码不能为空！
    pause
    exit /b 1
)

:: 设置JQL查询
echo.
echo 请选择查询类型:
echo 1. 简单查询 - 分配给我的未解决问题
echo 2. 复杂查询 - 特定状态的问题 (使用英文状态名)
echo 3. 复杂查询 - 特定状态的问题 (使用中文状态名)
echo 4. 自定义查询 - 手动输入JQL
echo.
set /p QUERY_TYPE="请选择 (1-4): "

if "!QUERY_TYPE!"=="1" (
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
    echo [选择] 简单查询: !JQL_QUERY!
) else if "!QUERY_TYPE!"=="2" (
    :: 使用英文状态名，避免中文编码问题
    set "JQL_QUERY=status in (\"To Do\",\"In Progress\",\"Open\") AND resolution = Unresolved AND assignee = currentUser()"
    echo [选择] 复杂查询(英文): !JQL_QUERY!
) else if "!QUERY_TYPE!"=="3" (
    :: 使用中文状态名，但进行特殊处理
    set "JQL_QUERY=status in (\"New Analysis\",\"Supplier Inbox\",\"Rejected\") AND resolution = Unresolved AND assignee = currentUser()"
    echo [选择] 复杂查询(中文): !JQL_QUERY!
) else if "!QUERY_TYPE!"=="4" (
    echo 请输入自定义JQL查询:
    set /p JQL_QUERY=
    if "!JQL_QUERY!"=="" (
        echo [错误] JQL查询不能为空！
        pause
        exit /b 1
    )
    echo [选择] 自定义查询: !JQL_QUERY!
) else (
    echo [错误] 无效选择，使用默认查询
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
)

echo.
echo [信息] Jira服务器: !JIRA_URL!
echo [信息] 用户名: !USERNAME!
echo [信息] JQL查询: !JQL_QUERY!
echo.

:: 构建API请求URL
set "API_URL=!JIRA_URL!/rest/api/2/search"
set "TEMP_FILE=%TEMP%\jira_response_%RANDOM%.json"

echo [信息] 正在查询Jira问题...

:: 发送API请求，添加UTF-8相关头部
curl -s -u "!USERNAME!:!PASSWORD!" ^
     -H "Accept: application/json; charset=utf-8" ^
     -H "Content-Type: application/json; charset=utf-8" ^
     -H "Accept-Charset: utf-8" ^
     -G "!API_URL!" ^
     --data-urlencode "jql=!JQL_QUERY!" ^
     --data-urlencode "maxResults=0" ^
     --data-urlencode "fields=summary" ^
     -o "!TEMP_FILE!" ^
     --connect-timeout 30 ^
     --max-time 60

set CURL_EXIT_CODE=%errorlevel%

:: 检查curl执行结果
if !CURL_EXIT_CODE! neq 0 (
    echo [错误] API请求失败！curl退出代码: !CURL_EXIT_CODE!
    if exist "!TEMP_FILE!" del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 检查响应文件
if not exist "!TEMP_FILE!" (
    echo [错误] 响应文件未生成！
    pause
    exit /b 1
)

:: 显示响应文件大小
for %%F in ("!TEMP_FILE!") do set "FILE_SIZE=%%~zF"
echo [调试] 响应文件大小: !FILE_SIZE! 字节

:: 检查是否是HTML响应
findstr /i "<html>\|<body>\|<title>" "!TEMP_FILE!" >nul
if !errorlevel! equ 0 (
    echo [错误] 收到HTML响应，可能是认证失败！
    echo [调试] 响应内容:
    type "!TEMP_FILE!"
    del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 显示原始响应内容（用于调试编码问题）
echo.
echo [调试] 原始响应内容（前500字符）:
echo ----------------------------------------
powershell -Command "Get-Content '!TEMP_FILE!' -Encoding UTF8 -Raw | Select-Object -First 1 | ForEach-Object { $_.Substring(0, [Math]::Min(500, $_.Length)) }"
echo ----------------------------------------
echo.

:: 使用PowerShell解析JSON，指定UTF-8编码
echo [信息] 正在解析响应数据...
for /f "delims=" %%i in ('powershell -Command "$ErrorActionPreference='Stop'; try { $content = Get-Content '!TEMP_FILE!' -Encoding UTF8 -Raw; $json = $content | ConvertFrom-Json; Write-Output $json.total } catch { Write-Output 'JSON_ERROR' }"') do (
    set TOTAL_ISSUES=%%i
)

:: 检查解析结果
if "!TOTAL_ISSUES!"=="JSON_ERROR" (
    echo [错误] JSON解析失败！
    echo.
    echo [调试] 尝试不同编码方式:
    echo.
    echo [UTF-8编码]:
    powershell -Command "Get-Content '!TEMP_FILE!' -Encoding UTF8 | Select-Object -First 5"
    echo.
    echo [默认编码]:
    powershell -Command "Get-Content '!TEMP_FILE!' | Select-Object -First 5"
    echo.
    echo [ASCII编码]:
    powershell -Command "Get-Content '!TEMP_FILE!' -Encoding ASCII | Select-Object -First 5"
    
    :: 保存错误响应
    set "ERROR_FILE=jira_encoding_error_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.json"
    set "ERROR_FILE=!ERROR_FILE: =0!"
    copy "!TEMP_FILE!" "!ERROR_FILE!" >nul
    echo.
    echo 错误响应已保存到: !ERROR_FILE!
    
    del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 清理临时文件
del "!TEMP_FILE!"

if "!TOTAL_ISSUES!"=="" (
    echo [错误] 无法获取问题数量！
    pause
    exit /b 1
)

:: 显示结果
echo.
echo ========================================
echo           查询结果
echo ========================================
echo 查询条件: !JQL_QUERY!
echo 问题剩余数量: !TOTAL_ISSUES!
echo 查询时间: %date% %time%
echo ========================================
echo.

:: 保存结果
set /p SAVE_RESULT="是否将结果保存到文件？(y/n): "
if /i "!SAVE_RESULT!"=="y" (
    set "RESULT_FILE=jira_issues_count_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "RESULT_FILE=!RESULT_FILE: =0!"
    
    :: 使用UTF-8编码保存文件
    powershell -Command "$content = @(); $content += '查询时间: %date% %time%'; $content += '查询条件: !JQL_QUERY!'; $content += '问题剩余数量: !TOTAL_ISSUES!'; $content | Out-File -FilePath '!RESULT_FILE!' -Encoding UTF8"
    
    echo [信息] 结果已保存到: !RESULT_FILE! (UTF-8编码)
)

echo.
echo ========================================
echo 脚本执行完成！
echo.
echo 编码说明:
echo - 脚本已设置为UTF-8编码模式
echo - 如果仍有乱码，请检查Jira中的实际状态名称
echo - 建议使用英文状态名避免编码问题
echo ========================================

echo 按任意键退出...
pause >nul
