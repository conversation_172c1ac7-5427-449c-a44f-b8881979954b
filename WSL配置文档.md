# WSL安装和配置手顺

1. **启用WSL功能**
   - 打开“控制面板”。
   - 选择“程序” > “启用或关闭Windows功能”。
   - 在弹出的窗口中，找到并勾选“适用于Linux的Windows子系统”。
   - 点击“确定”，然后重启计算机。

2. **安装Linux发行版**
   - 打开Microsoft Store。
   - 搜索你想要的Linux发行版（如Ubuntu、Debian等）。
   - 点击“获取”或“安装”按钮，等待下载完成。

3. **初始化Linux发行版**
   - 安装完成后，打开开始菜单，找到你安装的Linux发行版并启动。
   - 第一次启动时，会提示你创建一个新的用户账户和密码。

4. **更新Linux系统**
   - 在Linux终端中，运行以下命令以更新软件包：
     ```bash
     sudo apt update
     sudo apt upgrade
     ```

5. **配置WSL版本（可选）**
   - 如果你想使用WSL 2，确保你的Windows版本支持WSL 2（Windows 10 1903及以上版本）。
   - 在PowerShell中运行以下命令以设置WSL 2为默认版本：
     ```powershell
     wsl --set-default-version 2
     ```

6. **安装常用工具**
   - 根据需要安装常用的开发工具，例如Git、Node.js等：
     ```bash
     sudo apt install git
     sudo apt install nodejs npm
     ```

7. **访问Windows文件系统**
   - 在WSL中，你可以通过`/mnt/c`访问Windows的C盘文件系统。

8. **配置环境变量（可选）**
   - 如果需要，可以在`~/.bashrc`或`~/.bash_profile`中添加环境变量。

### 结论
通过以上步骤，你可以成功安装和配置WSL，享受在Windows上使用Linux的便利。根据个人需求，你可以进一步自定义和优化你的WSL环境。