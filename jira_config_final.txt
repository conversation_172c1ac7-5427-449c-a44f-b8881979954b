# Jira配置文件 - 最终版本
# 经过测试，连接正常

JIRA_URL=https://globaljira.geely.com
USERNAME=<EMAIL>
API_TOKEN=NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762

# JQL查询选项（取消注释您想要的查询）:

# 选项1: 简单查询 - 分配给我的未解决问题
# JQL_QUERY=assignee = currentUser() AND resolution = Unresolved

# 选项2: 您的原始复杂查询
JQL_QUERY=status in ("New Analysis","Supplier Inbox","Rejected") AND resolution = Unresolved AND labels in (APA) AND assignee in (e-pengxiaolong) ORDER BY updated DESC

# 选项3: 按优先级查询
# JQL_QUERY=assignee = currentUser() AND priority in ("High","Critical") AND resolution = Unresolved

# 选项4: 按项目查询（请替换PROJECT_KEY为实际项目键）
# JQL_QUERY=project = "PROJECT_KEY" AND assignee = currentUser() AND resolution = Unresolved

# 选项5: 最近更新的问题
# JQL_QUERY=assignee = currentUser() AND resolution = Unresolved AND updated >= -7d ORDER BY updated DESC
