# UE4开发技能栈

## 基础知识

### 编程语言
- **C++**：UE4的核心开发语言，用于高性能和底层功能实现
  - 现代C++特性（C++11/14/17）
  - 内存管理和垃圾回收机制
  - 多线程编程
  - 设计模式
  
- **蓝图(Blueprint)**：UE4的可视化脚本系统
  - 蓝图类与接口
  - 事件图表
  - 宏与函数库
  - 蓝图通信机制

### 引擎基础
- **UE4编辑器使用**
  - 界面布局与自定义
  - 项目设置与配置
  - 资产管理
  - 场景编辑

- **UE4架构理解**
  - 游戏框架
  - 模块系统
  - 对象系统
  - 反射系统
  - 委托机制

## 核心技术

### 图形与渲染
- **材质系统**
  - PBR材质原理
  - 材质编辑器使用
  - 材质函数与材质实例
  - 材质表达式
  
- **着色器编程**
  - HLSL基础
  - 自定义着色器
  - 后处理效果
  
- **渲染管线**
  - 延迟渲染
  - 前向渲染
  - 移动渲染路径
  - 光照与阴影系统
  
- **特效系统**
  - 粒子系统(Niagara/Cascade)
  - VFX图表
  - 体积雾与大气效果

### 物理与模拟
- **物理系统**
  - 刚体物理
  - 布料模拟
  - 破坏系统(Chaos)
  - 流体模拟
  
- **碰撞系统**
  - 碰撞检测
  - 物理材质
  - 射线检测(Raycast)

### 动画系统
- **骨骼动画**
  - 骨骼网格体
  - 动画蓝图
  - 状态机
  - 混合空间
  
- **高级动画技术**
  - IK(反向动力学)
  - 动画重定向
  - 动作匹配(Motion Matching)
  - 程序化动画

### 人工智能
- **行为树**
  - AI控制器
  - 黑板与行为树
  - 任务与装饰器
  
- **导航系统**
  - 导航网格(NavMesh)
  - 寻路算法
  - 环境查询系统(EQS)

## 游戏开发专项

### 游戏框架
- **GameMode与GameState**
- **PlayerController与Pawn/Character**
- **HUD与UI系统**
- **存档与读取系统**

### 多人游戏
- **网络复制**
  - 属性复制
  - RPC(远程过程调用)
  - 网络优化
  
- **会话与匹配**
  - 游戏会话创建
  - 玩家匹配系统

### 性能优化
- **性能分析工具**
  - 统计命令
  - 性能分析器
  
- **优化技术**
  - LOD(细节层次)
  - 实例化静态网格体
  - 纹理流送
  - 垃圾回收优化
  - 内存管理

## 工具链与集成

### 版本控制
- **Git/SVN/Perforce**
- **UE4版本控制最佳实践**

### 构建系统
- **UnrealBuildTool**
- **自动化构建与部署**

### 第三方集成
- **插件开发**
- **中间件集成**
  - FMOD/Wwise(音频)
  - Speedtree(植被)
  - Houdini Engine(程序化内容)

### 跨平台开发
- **平台特定API**
- **平台优化策略**

## 相关技能

### 3D建模与美术
- **基础3D建模知识**
- **UV展开与纹理贴图**
- **骨骼绑定**

### 游戏设计
- **关卡设计**
- **游戏机制设计**
- **用户体验**

### 项目管理
- **敏捷开发**
- **任务跟踪**
- **团队协作**

## 学习资源

### 官方资源
- UE4官方文档
- 虚幻学院(Unreal Learning)
- 官方示例项目

### 社区资源
- 论坛与社区
- 开源项目
- 视频教程

## 职业发展路径
- 游戏玩法程序员
- 图形/渲染工程师
- 技术美术(Technical Artist)
- 引擎程序员
- 工具开发工程师
