@echo off
setlocal enabledelayedexpansion

:: ========================================
:: Jira问题剩余数量获取脚本 (最终版本)
:: 作者: Assistant
:: 版本: 2.0
:: 描述: 通过Jira REST API获取问题剩余数量 (使用密码认证)
:: ========================================

echo.
echo ========================================
echo    Jira 问题剩余数量获取工具
echo ========================================
echo.

:: 配置信息
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

:: 安全地获取密码
echo 请输入您的Jira密码:
set /p PASSWORD=

if "!PASSWORD!"=="" (
    echo [错误] 密码不能为空！
    pause
    exit /b 1
)

:: 设置JQL查询
echo.
echo 请选择查询类型:
echo 1. 简单查询 - 分配给我的未解决问题
echo 2. 复杂查询 - 特定状态的问题 (New Analysis, Supplier Inbox, Rejected)
echo 3. 自定义查询 - 手动输入JQL
echo.
set /p QUERY_TYPE="请选择 (1-3): "

if "!QUERY_TYPE!"=="1" (
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
    echo [选择] 简单查询: !JQL_QUERY!
) else if "!QUERY_TYPE!"=="2" (
    set "JQL_QUERY=status in (\"New Analysis\",\"Supplier Inbox\",\"Rejected\") AND resolution = Unresolved AND assignee = currentUser()"
    echo [选择] 复杂查询: !JQL_QUERY!
) else if "!QUERY_TYPE!"=="3" (
    echo 请输入自定义JQL查询:
    set /p JQL_QUERY=
    if "!JQL_QUERY!"=="" (
        echo [错误] JQL查询不能为空！
        pause
        exit /b 1
    )
    echo [选择] 自定义查询: !JQL_QUERY!
) else (
    echo [错误] 无效选择，使用默认查询
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
)

echo.
echo [信息] Jira服务器: !JIRA_URL!
echo [信息] 用户名: !USERNAME!
echo [信息] JQL查询: !JQL_QUERY!
echo.

:: 构建API请求URL
set "API_URL=!JIRA_URL!/rest/api/2/search"
set "TEMP_FILE=%TEMP%\jira_response_%RANDOM%.json"

echo [信息] 正在查询Jira问题...

:: 发送API请求
curl -s -u "!USERNAME!:!PASSWORD!" ^
     -H "Accept: application/json" ^
     -G "!API_URL!" ^
     --data-urlencode "jql=!JQL_QUERY!" ^
     --data-urlencode "maxResults=0" ^
     --data-urlencode "fields=summary" ^
     -o "!TEMP_FILE!" ^
     --connect-timeout 30 ^
     --max-time 60

set CURL_EXIT_CODE=%errorlevel%

:: 检查curl执行结果
if !CURL_EXIT_CODE! neq 0 (
    echo [错误] API请求失败！curl退出代码: !CURL_EXIT_CODE!
    if exist "!TEMP_FILE!" del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 检查响应文件
if not exist "!TEMP_FILE!" (
    echo [错误] 响应文件未生成！
    pause
    exit /b 1
)

:: 检查是否是HTML响应
findstr /i "<html>\|<body>\|<title>" "!TEMP_FILE!" >nul
if !errorlevel! equ 0 (
    echo [错误] 收到HTML响应，可能是认证失败！
    del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 解析JSON响应获取总数
for /f "delims=" %%i in ('powershell -Command "try { (Get-Content '!TEMP_FILE!' | ConvertFrom-Json).total } catch { Write-Output 'ERROR' }"') do (
    set TOTAL_ISSUES=%%i
)

del "!TEMP_FILE!"

if "!TOTAL_ISSUES!"=="ERROR" (
    echo [错误] JSON解析失败！
    pause
    exit /b 1
)

:: 显示结果
echo.
echo ========================================
echo           查询结果
echo ========================================
echo 查询条件: !JQL_QUERY!
echo 问题剩余数量: !TOTAL_ISSUES!
echo 查询时间: %date% %time%
echo ========================================
echo.

:: 保存结果
set /p SAVE_RESULT="是否将结果保存到文件？(y/n): "
if /i "!SAVE_RESULT!"=="y" (
    set "RESULT_FILE=jira_issues_count_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "RESULT_FILE=!RESULT_FILE: =0!"
    echo 查询时间: %date% %time% > "!RESULT_FILE!"
    echo 查询条件: !JQL_QUERY! >> "!RESULT_FILE!"
    echo 问题剩余数量: !TOTAL_ISSUES! >> "!RESULT_FILE!"
    echo [信息] 结果已保存到: !RESULT_FILE!
)

echo.
echo 脚本执行完成！
pause
