### 1. 创建插件项目

首先，创建一个新的插件项目，并添加必要的头文件和源文件。

### 2. 实现插件功能

#### `MyPlugin.hpp`

```cpp:MyPlugin/MyPlugin.hpp
#pragma once
#include <kanzi/kanzi.hpp>

class MyPlugin : public kz::Node
{
    KZ_DECLARE_NODE(MyPlugin)
public:
    MyPlugin();
    ~MyPlugin();

    void update(float deltaTime) override;
    void onTouchMove(const kz::TouchEvent& event);

private:
    kz::vec2 m_lastTouchPosition;
    kz::vec3 m_rotation;
};
```

#### `MyPlugin.cpp`

```cpp:MyPlugin/MyPlugin.cpp
#include "MyPlugin.hpp"

KZ_IMPLEMENT_NODE(MyPlugin)

MyPlugin::MyPlugin()
{
    // 初始化节点
    m_lastTouchPosition = kz::vec2(0.0f, 0.0f);
    m_rotation = kz::vec3(0.0f, 0.0f, 0.0f);

    // 注册触摸事件监听器
    this->addEventListener<kz::TouchEvent>([this](const kz::TouchEvent& event) {
        if (event.getType() == kz::TouchEvent::TOUCH_MOVE)
        {
            this->onTouchMove(event);
        }
    });
}

MyPlugin::~MyPlugin()
{
    // 清理节点资源
}

void MyPlugin::update(float deltaTime)
{
    // 更新节点逻辑
    this->setRotation(m_rotation);
}

void MyPlugin::onTouchMove(const kz::TouchEvent& event)
{
    kz::vec2 currentTouchPosition = event.getPosition();
    kz::vec2 delta = currentTouchPosition - m_lastTouchPosition;

    // 根据触摸移动的距离来更新旋转角度
    m_rotation.x += delta.y * 0.1f; // 乘以一个系数来调整旋转速度
    m_rotation.y += delta.x * 0.1f;

    m_lastTouchPosition = currentTouchPosition;
}
```

#### `PluginInitialization.cpp`

```cpp:MyPlugin/PluginInitialization.cpp
#include "MyPlugin.hpp"
#include <kanzi/kanzi.hpp>

// 插件初始化函数
KZ_PLUGIN_EXPORT void initializePlugin(kz::Engine& engine)
{
    // 注册自定义节点
    engine.registerNode<MyPlugin>();
}

// 插件终止函数
KZ_PLUGIN_EXPORT void uninitializePlugin(kz::Engine& engine)
{
    // 清理插件资源
}
```

### 3. 编译插件

使用你的开发环境编译插件项目，生成一个动态链接库（DLL）或共享库（.so 文件）。

### 4. 加载插件

将生成的库文件放置在 Kanzi 项目中，并在 Kanzi Studio 或代码中加载插件。

### 5. 使用插件

在 Kanzi Studio 中，将你的自定义节点（`MyPlugin`）添加到场景中，并确保模型是该节点的子节点。这样，触摸事件将会影响模型的旋转。