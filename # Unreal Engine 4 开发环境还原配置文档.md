# Unreal Engine 4 开发环境还原配置文档

## 1. 环境准备

### 1.1 安装 Unreal Engine 4

1. **下载 Epic Games Launcher**:
   - 访问 [Epic Games 官网](https://www.unrealengine.com/en-US/download)。
   - 点击 **Download** 按钮，下载 Epic Games Launcher 安装程序。

2. **安装 Epic Games Launcher**:
   - 双击下载的安装程序，按照提示完成安装。

3. **安装 Unreal Engine 4**:
   - 启动 Epic Games Launcher，登录或注册一个 Epic Games 账户。
   - 在左侧菜单中选择 **Unreal Engine**，然后点击 **Library** 标签。
   - 点击 **+ Add Versions** 按钮，选择你想要安装的 Unreal Engine 版本（建议选择最新的稳定版本），然后点击 **Install**。

### 1.2 安装 Visual Studio

1. **下载 Visual Studio**:
   - 访问 [Visual Studio 官网](https://visualstudio.microsoft.com/)。
   - 点击 **Download** 按钮，选择 **Community** 版本（免费）。

2. **安装 Visual Studio**:
   - 双击下载的安装程序，启动 Visual Studio Installer。
   - 在安装过程中，选择 **Desktop development with C++** 工作负载。
   - 确保勾选以下组件：
     - **MSVC v142 - VS 2019 C++ x64/x86 build tools**（或最新版本）。
     - **Windows 10 SDK**（建议选择最新版本）。
     - **C++ CMake tools for Windows**（可选，但推荐）。

3. **完成安装**:
   - 点击 **Install** 按钮，等待安装完成。

### 1.3 安装必要的插件（可选）

1. **安装 Git**（用于版本控制）:
   - 访问 [Git 官网](https://git-scm.com/downloads)。
   - 下载并安装适合你操作系统的 Git 版本。

2. **安装 Perforce**（用于团队协作）:
   - 访问 [Perforce 官网](https://www.perforce.com/downloads/helix-core-apps)。
   - 下载并安装 Helix Core 客户端。

## 2. 项目配置

### 2.1 创建新项目

1. **打开 Unreal Engine 4**:
   - 启动 Epic Games Launcher，点击 **Launch** 按钮启动 Unreal Engine。

2. **创建新项目**:
   - 在启动界面，选择 **New Project**。
   - 选择项目类型（如 **Games**、**Film**、**Architecture** 等）。
   - 选择项目模板（如 **First Person**、**Third Person**、**Blank** 等）。

3. **配置项目设置**:
   - **Blueprint** 或 **C++**: 选择你想要的项目类型。
   - **项目名称**: 输入项目名称，例如 `MyFirstGame`。
   - **保存路径**: 选择项目保存的位置。
   - 点击 **Create** 按钮，等待项目创建完成。

### 2.2 配置项目设置

1. **进入项目设置**:
   - 在主菜单中，选择 **Edit** -> **Project Settings**。

2. **配置项目选项**:
   - **Maps & Modes**:
     - 设置默认地图（例如选择 `MainMenu` 或 `Game`）。
     - 设置默认游戏模式（例如选择 `GameModeBase`）。
   - **Input**:
     - 配置输入绑定，例如添加移动、跳跃等操作。
   - **Rendering**:
     - 根据需要调整渲染设置，例如启用或禁用抗锯齿。
   - **Packaging**:
     - 配置打包选项，如目标平台（Windows、Android、iOS 等）和包名（例如 `com.yourcompany.yourproject`）。

## 3. 版本控制配置

### 3.1 使用 Git 进行版本控制

1. **初始化 Git 仓库**:
   - 打开命令提示符（Windows）或终端（Mac）。
   - 导航到你的项目根目录，输入以下命令：
     ```bash
     git init
     ```

2. **创建 `.gitignore` 文件**:
   - 在项目根目录下创建一个名为 `.gitignore` 的文件，添加以下内容以忽略不必要的文件：
     ```
     Binaries/
     DerivedDataCache/
     Intermediate/
     Saved/
     *.sln
     *.suo
     *.user
     *.userprefs
     ```

3. **提交初始版本**:
   - 在命令提示符中，输入以下命令：
     ```bash
     git add .
     git commit -m "Initial commit"
     ```

### 3.2 使用 Perforce 进行版本控制（可选）

1. **安装并配置 Perforce**:
   - 启动 Perforce Helix Core 客户端，创建一个新的工作区。

2. **在 Unreal Engine 中配置 Perforce**:
   - 进入 **Edit** -> **Editor Preferences**，选择 **Source Control**。
   - 选择 **Perforce**，输入服务器地址和用户凭据。

## 4. 常见问题

- **项目无法打开**: 确保 Unreal Engine 版本与项目版本一致。如果项目是用较新版本创建的，旧版本可能无法打开。
- **编译错误**: 检查 Visual Studio 是否正确安装，并确保所有必要的组件已安装。如果有缺失的组件，重新运行 Visual Studio Installer 进行修复。
- **无法找到 SDK**: 确保 Windows 10 SDK 已正确安装，并在项目设置中配置了正确的路径。

## 5. 参考链接

- [Unreal Engine Documentation](https://docs.unrealengine.com/en-US/index.html)
- [Visual Studio Documentation](https://docs.microsoft.com/en-us/visualstudio/?view=vs-2019)
- [Git Documentation](https://git-scm.com/doc)
- [Perforce Documentation](https://www.perforce.com/manuals)

