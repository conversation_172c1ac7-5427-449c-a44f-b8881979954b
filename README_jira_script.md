# Jira问题剩余数量获取脚本

这是一个Windows批处理脚本，用于通过Jira REST API获取问题剩余数量。

## 文件说明

- `get_jira_issues.bat` - 主要的批处理脚本
- `jira_config.txt` - 配置文件，包含Jira连接信息
- `README_jira_script.md` - 本说明文档

## 功能特性

- ✅ 通过Jira REST API获取问题数量
- ✅ 支持自定义JQL查询条件
- ✅ 安全的API Token认证
- ✅ 友好的中文界面
- ✅ 错误处理和验证
- ✅ 可选的结果保存功能
- ✅ 使用Windows内置工具（curl和PowerShell）

## 使用前准备

### 1. 获取Jira API Token

1. 登录您的Jira实例
2. 点击右上角头像 → **账户设置**
3. 选择 **安全** 标签
4. 点击 **创建和管理API令牌**
5. 点击 **创建API令牌**
6. 输入标签名称（如"脚本访问"）
7. 复制生成的API令牌（请妥善保存）

### 2. 配置脚本

编辑 `jira_config.txt` 文件，填入您的信息：

```
JIRA_URL=https://your-company.atlassian.net
USERNAME=<EMAIL>
API_TOKEN=your-api-token-here
JQL_QUERY=assignee = currentUser() AND resolution = Unresolved
```

### 3. 系统要求

- Windows 10/11（内置curl和PowerShell）
- 或者手动安装curl工具
- 网络连接到Jira服务器

## 使用方法

1. 双击运行 `get_jira_issues.bat`
2. 脚本会自动读取配置并查询Jira
3. 查看显示的问题剩余数量
4. 可选择是否将结果保存到文件

## JQL查询示例

在 `jira_config.txt` 中的 `JQL_QUERY` 可以使用以下示例：

### 基本查询
```
# 查询分配给当前用户的未解决问题
assignee = currentUser() AND resolution = Unresolved

# 查询特定项目的未解决问题
project = "PROJ" AND resolution = Unresolved

# 查询所有高优先级问题
priority in ("High", "Critical")
```

### 状态查询
```
# 查询进行中的问题
status = "In Progress"

# 查询待办和进行中的问题
status in ("To Do", "In Progress")
```

### 时间范围查询
```
# 查询本周创建的问题
created >= startOfWeek()

# 查询最近30天更新的问题
updated >= -30d
```

### 组合查询
```
# 查询分配给我的高优先级未解决问题
assignee = currentUser() AND priority in ("High", "Critical") AND resolution = Unresolved

# 查询特定项目中我负责的进行中问题
project = "MYPROJECT" AND assignee = currentUser() AND status = "In Progress"
```

## 故障排除

### 常见错误及解决方法

#### 1. "curl 命令不可用"
**解决方法：**
- 确保使用Windows 10/11（内置curl）
- 或下载安装curl工具

#### 2. "API请求失败"
**可能原因：**
- 网络连接问题
- Jira URL错误
- 用户名或API Token错误

**解决方法：**
- 检查网络连接
- 验证 `jira_config.txt` 中的配置信息
- 确认API Token是否有效

#### 3. "无法解析响应数据"
**可能原因：**
- 认证失败
- JQL查询语法错误
- Jira返回错误响应

**解决方法：**
- 检查用户名和API Token
- 验证JQL查询语法
- 在Jira界面中测试相同的JQL查询

#### 4. "配置文件不存在"
**解决方法：**
- 确保 `jira_config.txt` 文件存在
- 检查文件名拼写是否正确

## 安全注意事项

1. **保护API Token**
   - 不要将API Token分享给他人
   - 不要将包含API Token的配置文件提交到版本控制系统
   - 定期更换API Token

2. **文件权限**
   - 确保配置文件只有您可以访问
   - 考虑将配置文件放在安全的位置

## 高级用法

### 定时执行
可以结合Windows任务计划程序实现定时查询：
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器（如每天、每小时）
4. 设置操作为运行此脚本

### 结果处理
脚本支持将结果保存到文件，文件名格式：
`jira_issues_count_YYYYMMDD_HHMMSS.txt`

### 批量查询
可以修改脚本支持多个JQL查询，或创建多个配置文件。

## 技术说明

- 使用Jira REST API v2
- 通过curl发送HTTP请求
- 使用PowerShell解析JSON响应
- 支持基本认证（用户名+API Token）

## 版本历史

- v1.0 - 初始版本，支持基本的问题数量查询功能

## 联系支持

如果遇到问题，请检查：
1. Jira服务器是否可访问
2. 配置信息是否正确
3. API Token是否有效
4. JQL查询语法是否正确

更多JQL语法帮助，请参考Jira官方文档。
