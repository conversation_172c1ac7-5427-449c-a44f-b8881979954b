@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    Jira 状态名称检查工具
echo ========================================
echo.

set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

echo 请输入您的Jira密码:
set /p PASSWORD=

echo.
echo 正在获取Jira中的所有状态名称...
echo.

:: 获取所有状态
set "STATUS_FILE=%TEMP%\jira_status_%RANDOM%.json"
curl -s -u "!USERNAME!:!PASSWORD!" ^
     -H "Accept: application/json; charset=utf-8" ^
     "!JIRA_URL!/rest/api/2/status" ^
     -o "!STATUS_FILE!"

if exist "!STATUS_FILE!" (
    echo [信息] 系统中的所有状态:
    echo ========================================
    
    :: 使用PowerShell解析状态列表
    powershell -Command "
    $ErrorActionPreference = 'Stop'
    try {
        $content = Get-Content '!STATUS_FILE!' -Encoding UTF8 -Raw
        $statuses = $content | ConvertFrom-Json
        Write-Output '状态ID | 状态名称 | 状态类别'
        Write-Output '-------|----------|----------'
        foreach ($status in $statuses) {
            $id = $status.id
            $name = $status.name
            $category = $status.statusCategory.name
            Write-Output ('{0,6} | {1,-20} | {2}' -f $id, $name, $category)
        }
    } catch {
        Write-Output '解析状态列表失败，显示原始内容:'
        Get-Content '!STATUS_FILE!' -Encoding UTF8
    }"
    
    del "!STATUS_FILE!"
) else (
    echo [错误] 无法获取状态列表
)

echo.
echo ========================================
echo.

:: 获取您的问题示例
echo 正在获取您的问题示例（查看实际使用的状态）...
set "SAMPLE_FILE=%TEMP%\jira_sample_%RANDOM%.json"
curl -s -u "!USERNAME!:!PASSWORD!" ^
     -H "Accept: application/json; charset=utf-8" ^
     -G "!JIRA_URL!/rest/api/2/search" ^
     --data-urlencode "jql=assignee = currentUser()" ^
     --data-urlencode "maxResults=10" ^
     --data-urlencode "fields=summary,status" ^
     -o "!SAMPLE_FILE!"

if exist "!SAMPLE_FILE!" (
    echo.
    echo [信息] 您的问题中使用的状态:
    echo ========================================
    
    powershell -Command "
    $ErrorActionPreference = 'Stop'
    try {
        $content = Get-Content '!SAMPLE_FILE!' -Encoding UTF8 -Raw
        $result = $content | ConvertFrom-Json
        Write-Output '问题摘要 | 状态'
        Write-Output '---------|------'
        foreach ($issue in $result.issues) {
            $summary = $issue.fields.summary
            $status = $issue.fields.status.name
            if ($summary.Length -gt 30) { $summary = $summary.Substring(0, 30) + '...' }
            Write-Output ('{0,-35} | {1}' -f $summary, $status)
        }
        
        Write-Output ''
        Write-Output '唯一状态列表:'
        $uniqueStatuses = $result.issues | ForEach-Object { $_.fields.status.name } | Sort-Object | Get-Unique
        foreach ($status in $uniqueStatuses) {
            Write-Output ('- {0}' -f $status)
        }
    } catch {
        Write-Output '解析问题列表失败，显示原始内容:'
        Get-Content '!SAMPLE_FILE!' -Encoding UTF8
    }"
    
    del "!SAMPLE_FILE!"
) else (
    echo [错误] 无法获取问题示例
)

echo.
echo ========================================
echo 使用建议:
echo.
echo 1. 使用上面显示的确切状态名称
echo 2. 如果状态名称包含中文，建议使用状态ID
echo 3. 状态名称区分大小写，必须完全匹配
echo.
echo 示例JQL查询:
echo - 使用状态名称: status in (\"状态名1\",\"状态名2\")
echo - 使用状态ID: status in (1,2,3)
echo ========================================

pause
