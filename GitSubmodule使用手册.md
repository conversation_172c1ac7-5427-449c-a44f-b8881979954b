# Git Submodule 使用手册

Git Submodule 是 Git 中的一种功能，允许你在一个 Git 仓库中包含另一个 Git 仓库。它非常适合在项目中使用外部库或依赖项。以下是如何使用 Git Submodule 的基本步骤：

## 1. 添加 Submodule

要在你的项目中添加一个 submodule，可以使用以下命令：

```bash
git submodule add <repository_url> <path>
```

- `<repository_url>` 是你想要添加的子模块的 Git 仓库 URL。
- `<path>` 是你希望子模块存放在主项目中的路径。

例如：

```bash
git submodule add https://github.com/example/library.git libs/library
```

## 2. 初始化和更新 Submodule

当你克隆一个包含子模块的仓库时，子模块的内容不会自动被克隆。你需要初始化并更新它们：

```bash
git submodule init
git submodule update
```

或者可以使用一条命令完成：

```bash
git submodule update --init --recursive
```

## 3. 更新 Submodule

如果子模块的远程仓库有更新，你可以在子模块目录中拉取最新的更改：

```bash
cd <path_to_submodule>
git pull origin main
```

然后回到主项目目录，提交子模块的更新：

```bash
cd ..
git add <path_to_submodule>
git commit -m "Update submodule"
```

## 4. 删除 Submodule

要从项目中删除一个子模块，步骤如下：

1. 删除子模块的条目：

   ```bash
   git submodule deinit -f -- <path_to_submodule>
   ```

2. 删除子模块的目录：

   ```bash
   rm -rf <path_to_submodule>
   ```

3. 移除 `.gitmodules` 文件中的子模块条目：

   打开 `.gitmodules` 文件，删除相关的子模块条目。

4. 移除 Git 配置中的子模块条目：

   ```bash
   git rm --cached <path_to_submodule>
   ```

5. 提交更改：

   ```bash
   git commit -m "Remove submodule"
   ```

## 5. 常见问题

- **子模块未初始化**：确保在克隆仓库后运行 `git submodule update --init --recursive`。
- **子模块版本不对**：检查子模块目录中的 `.git` 文件，确保指向正确的 commit。
