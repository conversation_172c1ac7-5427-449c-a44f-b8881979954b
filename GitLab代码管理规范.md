# GitLab代码管理规范

## 目录

### 第一部分：Git基础（个人开发）
- [1. Git常用命令](#1-git常用命令)
- [2. 分支命名规范](#2-分支命名规范)
- [3. 提交消息规范](#3-提交消息规范)

### 第二部分：基本工作流（团队协作基础）
- [4. 分支管理策略](#4-分支管理策略)
- [5. 仓库Fork与合并流程](#5-仓库fork与合并流程)
- [6. 合并请求规范](#6-合并请求规范)

### 第三部分：团队管理（协作规范）
- [7. 代码审查流程](#7-代码审查流程)
- [8. 项目权限管理](#8-项目权限管理)
- [9. 版本标签管理](#9-版本标签管理)

### 第四部分：高级特性（复杂场景）
- [10. Submodule管理规范](#10-submodule管理规范)
- [11. Git LFS管理规范](#11-git-lfs管理规范)
- [12. CI/CD集成规范](#12-cicd集成规范)
- [13. 里程碑与发版管理](#13-里程碑与发版管理)

### 第五部分：实用工具
- [14. 常见问题与解决方案](#14-常见问题与解决方案)

## 1. Git常用命令

在开始学习GitLab代码管理规范之前，我们先了解一些基础的Git命令。这些命令是日常开发中最常用的，掌握它们是进行版本控制的基础。

### 1.1 分支操作

```bash
# 查看所有分支
git branch -a

# 创建新分支
git checkout -b feature/new-feature

# 切换分支
git checkout develop

# 删除本地分支
git branch -d feature/old-feature

# 删除远程分支
git push origin --delete feature/old-feature
```

### 1.2 提交操作

```bash
# 查看状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "feat: 添加新功能"

# 修改最后一次提交
git commit --amend

# 推送到远程
git push origin feature/new-feature
```

### 1.3 远程仓库操作

```bash
# 添加远程仓库
git remote add origin https://gitlab.com/username/project-name.git

# 查看远程仓库
git remote -v

# 获取远程更新
git fetch origin

# 拉取并合并远程更新
git pull origin main

# 推送到远程仓库
git push origin main
```

### 1.4 查看历史和状态

```bash
# 查看提交历史
git log --oneline

# 查看文件差异
git diff

# 查看暂存区差异
git diff --cached

# 查看特定文件的修改历史
git log -p filename
```

## 2. 分支命名规范

### 2.1 分支前缀

- 功能分支：`feature/`
- 缺陷修复分支：`bugfix/`
- 热修复分支：`hotfix/`
- 发布分支：`release/`
- 文档分支：`docs/`
- 重构分支：`refactor/`

### 2.2 分支命名格式

```
<前缀>/<任务编号>-<简短描述>
```

### 2.3 命名示例

- `feature/123-user-login`
- `bugfix/456-fix-memory-leak`
- `hotfix/789-critical-security-issue`
- `release/v1.2.0`
- `docs/update-api-docs`
- `refactor/optimize-database-queries`

### 2.4 注意事项

- 分支名称使用小写字母
- 单词之间使用连字符 `-` 分隔
- 简短描述应简明扼要，不超过50个字符
- 尽量包含任务编号（如Jira/GitLab Issue ID）

## 3. 提交消息规范

### 3.1 提交消息格式

```
<类型>(<范围>): <主题>

<正文>

<页脚>
```

### 3.2 类型（Type）

| 类型 | 描述 | 示例场景 |
|------|------|----------|
| `feat` | 新功能 | 添加登录功能、实现支付模块 |
| `fix` | Bug修复 | 修复登录失败、解决内存泄漏 |
| `docs` | 文档更新 | 更新README、添加API文档 |
| `style` | 代码格式 | 修复缩进、移除空行 |
| `refactor` | 重构 | 优化算法、重构组件 |
| `perf` | 性能优化 | 提升查询效率、优化渲染 |
| `test` | 测试 | 添加单元测试、修复测试用例 |
| `build` | 构建系统 | 更新构建配置、修改构建脚本 |
| `ci` | CI/CD | 更新CI配置、修复部署流程 |
| `chore` | 其他 | 更新依赖、删除无用文件 |
| `revert` | 回滚 | 撤销之前的提交 |

### 3.3 范围（Scope）

描述本次提交影响的模块或组件，可选但推荐。

### 3.4 主题（Subject）

- 使用祈使句，现在时
- 首字母小写
- 不超过50个字符
- 不以句号结尾

### 3.5 正文（Body）

- 详细描述为什么做这个改动
- 说明实现方式
- 每行不超过72个字符
- 与主题之间空一行

### 3.6 页脚（Footer）

- 引用相关的Issue或PR
- 标记不兼容变更
- 格式：`Closes #123` 或 `BREAKING CHANGE: 描述不兼容变更`

## 4. 分支管理策略

在掌握了基本的Git命令和规范后，我们来学习分支管理策略。我们采用基于Git Flow的分支管理策略，这是团队协作的基础。

### 4.1 长期分支

- **master/main**: 主分支，存放稳定的生产环境代码
- **develop**: 开发分支，存放最新的开发代码

### 4.2 临时分支

- **feature**: 功能分支，用于开发新功能
- **bugfix**: 缺陷修复分支，用于修复非生产环境的缺陷
- **hotfix**: 热修复分支，用于修复生产环境的紧急缺陷
- **release**: 发布分支，用于版本发布前的准备工作
- **docs**: 文档分支，用于文档更新
- **refactor**: 重构分支，用于代码重构

### 4.3 分支工作流

1. 从 `develop` 分支创建功能分支进行开发
2. 开发完成后，提交合并请求到 `develop` 分支
3. 版本发布前，从 `develop` 分支创建 `release` 分支
4. 测试通过后，将 `release` 分支合并到 `master` 和 `develop` 分支
5. 生产环境出现紧急问题时，从 `master` 分支创建 `hotfix` 分支
6. 修复完成后，将 `hotfix` 分支合并到 `master` 和 `develop` 分支

### 4.4 分支管理最佳实践

- **保持分支简洁**：及时删除已合并的功能分支
- **定期同步**：定期从主分支拉取最新代码，避免冲突
- **分支命名一致性**：严格遵循命名规范，便于管理和识别
- **功能分支独立性**：每个功能分支只包含一个功能的开发

## 5. 仓库Fork与合并流程

学习了分支管理策略后，我们来了解Fork工作流。这是我们项目中采用的主要协作方式，确保代码质量和安全性。

### 5.1 Fork工作流概述

为了保证项目代码的质量和安全性，我们采用Fork工作流进行开发：

1. 开发者从项目主仓库Fork出个人仓库
2. 在个人仓库中进行开发
3. 开发完成后，通过合并请求（Merge Request）将代码合并回主仓库
4. 禁止直接向项目主仓库提交代码

### 5.2 Fork仓库设置流程

1. 在GitLab项目主页点击"Fork"按钮
2. 选择将仓库Fork到个人命名空间下
3. 克隆个人Fork的仓库到本地：
   ```bash
   git clone https://gitlab.com/your-username/project-name.git
   ```
4. 添加上游仓库作为远程仓库：
   ```bash
   git remote add upstream https://gitlab.com/organization/project-name.git
   ```

### 5.3 保持Fork仓库同步

定期从上游仓库同步更新：

```bash
# 获取上游仓库的最新更改
git fetch upstream

# 切换到本地主分支
git checkout main

# 合并上游主分支的更改
git merge upstream/main

# 推送到个人Fork仓库
git push origin main
```

### 5.4 从Fork仓库提交合并请求

1. 在个人Fork仓库中创建功能分支：
   ```bash
   git checkout -b feature/new-feature
   ```

2. 在功能分支上进行开发并提交更改：
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   git push origin feature/new-feature
   ```

3. 在GitLab界面中创建合并请求：
   - 源分支：个人Fork仓库的功能分支
   - 目标分支：项目主仓库的develop分支
   - 填写合并请求描述

4. 等待代码审查和合并

### 5.5 Fork仓库管理最佳实践

- 定期同步上游仓库的更改，避免合并冲突
- 为每个功能或修复创建单独的分支
- 在提交合并请求前，确保代码通过所有测试
- 合并请求应尽量小而集中，便于审查
- 合并完成后，删除已合并的功能分支

## 6. 合并请求规范

掌握了Fork工作流后，我们来学习如何规范地创建和管理合并请求（Merge Request）。这是代码审查和质量控制的关键环节。

### 6.1 合并请求标题

格式：`[<类型>] <简短描述> (#<任务编号>)`

示例：`[Feature] 实现用户登录功能 (#123)`

### 6.2 Issue关联规范

- **强制关联**：每个MR标题必须与对应的Issue进行关联
- **关联格式**：
  - `Fix: #1234 修复xxx问题` - 用于Bug修复
  - `Feature: #5678 新增xxx功能` - 用于新功能开发
  - `Closes #9999 完成xxx任务` - 用于任务完成
- **关联作用**：确保代码变更的可追溯性，便于项目管理和问题跟踪

### 6.3 MR状态管理

- **Hold状态使用**：当MR存在问题不允许合并时，应保持该MR处于Hold状态
- **持续更新原则**：待修复后继续更新同一MR，不应重新创建新的MR
- **唯一性保证**：避免同一目标存在多个MR，确保记录的唯一性和完整性
- **状态跟踪**：通过MR状态变更记录问题解决过程，便于后续追溯

### 6.4 合并请求描述模板

```markdown
## 功能描述
简要描述本次合并的功能或修复的问题

## 实现方案
描述实现方案和技术选择

## 测试情况
描述测试方法和测试结果

## 相关链接
- 关联的需求文档
- 关联的设计文档
- 关联的Issue

## 截图（如有）
添加相关截图
```

### 6.5 合并策略

- 功能分支合并到开发分支：使用 `Squash and merge`
- 发布分支合并到主分支：使用 `Merge commit`
- 热修复分支合并：使用 `Cherry-pick`

## 7. 代码审查流程

完成了基本工作流的学习后，我们进入团队管理部分。代码审查是确保代码质量的重要环节，也是团队知识共享的好机会。

### 7.1 审查原则

- 每个合并请求必须经过至少一名团队成员的审查
- 代码审查应关注代码质量、功能实现和安全性
- 审查应在提交合并请求后的24小时内完成

### 7.2 审查重点

- 代码是否符合项目编码规范
- 功能实现是否符合需求
- 是否有潜在的安全问题
- 是否有性能优化空间
- 测试覆盖率是否足够

### 7.3 审查流程

1. 开发者提交合并请求
2. 指定审查者进行代码审查
3. 审查者提出修改建议
4. 开发者根据建议进行修改
5. 审查者确认修改并批准合并
6. 项目负责人执行最终合并

### 7.4 权限要求

- **Maintainer权限**：具备合并MR、管理分支、配置CI等权限，通常分配给模块负责人
- **Reporter权限**：仅具备查看代码、提交Issue的权限，通常分配给普通研发人员
- **审查权限**：所有具备Maintainer权限的成员都可以进行代码审查
- **最终合并权限**：只有项目负责人或指定的Maintainer可以执行最终合并操作

## 8. 项目权限管理

在团队协作中，合理的权限管理是确保项目安全和高效运行的基础。我们需要建立清晰的权限层级和管理流程。

### 8.1 权限层级体系

#### 8.1.1 项目管理员

- **职责**：负责创建和管理顶层Group、分配权限、配置仓库
- **权限范围**：所有仓库的Owner权限
- **管理内容**：
  - 创建顶层Group（如：Org-Project）
  - 创建各模块代码仓库（android-repo、ue-repo等）
  - 创建共用代码仓库（common-repo）
  - 创建顶层集成仓库（integration-repo）

#### 8.1.2 项目负责人

- **职责**：负责项目整体技术决策和发版管理
- **权限范围**：顶层集成仓库的Maintainer权限，其他仓库的Reporter权限
- **管理内容**：
  - 审核和合并顶层集成仓库的MR
  - 管理项目里程碑和发版计划
  - 协调各模块间的集成工作

#### 8.1.3 模块负责人

- **职责**：负责各自模块的技术实现和代码质量
- **权限范围**：各自项目代码仓库的Maintainer权限
- **管理内容**：
  - 管理模块分支和合并MR
  - 配置和维护模块CI流程
  - 向顶层仓库提交依赖关系更新

#### 8.1.4 普通研发人员

- **职责**：负责具体功能开发和Bug修复
- **权限范围**：所有仓库的Reporter权限（只读）
- **工作方式**：
  - 通过Fork仓库进行开发
  - 通过MR提交代码变更
  - 参与代码审查和测试

### 8.2 权限分配流程

#### 8.2.1 初始权限分配

1. 项目管理员创建Group和仓库结构
2. 分配项目负责人为顶层集成仓库的Maintainer
3. 分配各模块负责人为对应仓库的Maintainer
4. 分配普通研发人员为所有仓库的Reporter
5. 分配共用代码负责人为common-repo的Maintainer

#### 8.2.2 权限变更管理

- **申请流程**：需要权限变更时，向项目管理员提出申请
- **审批机制**：由项目管理员和项目负责人共同审批
- **变更记录**：所有权限变更都需要记录在案，包括变更原因和时间
- **定期审查**：定期审查权限分配的合理性，及时调整不合适的权限

### 8.3 权限安全管理

#### 8.3.1 最小权限原则

- 每个用户只分配完成工作所需的最小权限
- 避免过度授权，降低安全风险
- 定期审查和回收不必要的权限

#### 8.3.2 分支保护策略

- **主分支保护**：master/main分支设置为受保护分支
- **开发分支保护**：develop分支设置推送规则和合并要求
- **强制审查**：所有受保护分支的合并都需要经过代码审查
- **CI要求**：合并前必须通过CI检查

#### 8.3.3 访问控制

- **SSH密钥管理**：要求使用SSH密钥进行身份验证
- **双因素认证**：建议启用双因素认证增强安全性
- **访问日志**：定期检查访问日志，发现异常访问行为

## 9. 版本标签管理

完成了权限管理的学习后，我们来了解版本标签管理。这是项目发布和版本控制的重要组成部分。

### 9.1 版本号规范

采用语义化版本（Semantic Versioning）规范：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的API变更
- **次版本号**：向下兼容的功能新增
- **修订号**：向下兼容的问题修复

### 9.2 标签命名规范

格式：`v<版本号>`

示例：`v1.2.3`

### 9.3 版本发布流程

1. 在 `release` 分支上完成版本准备工作
2. 更新版本号和更新日志
3. 合并到 `master` 分支
4. 在 `master` 分支上创建标签
5. 推送标签到远程仓库

### 9.4 版本管理最佳实践

- **版本规划**：提前规划版本发布计划和功能范围
- **变更日志**：为每个版本维护详细的变更日志
- **标签保护**：设置标签保护规则，防止误删除或修改
- **发布说明**：为每个版本编写清晰的发布说明

## 10. Submodule管理规范

进入高级特性部分，我们首先学习Submodule管理。这是处理复杂项目依赖和多仓库协作的重要工具。

### 10.1 使用Submodule的场景

在以下场景中，我们使用Git Submodule管理项目依赖：

- 多个项目共享同一个组件或库
- 项目依赖于特定版本的外部库
- 需要跟踪和管理多个相关仓库
- 管理从主项目Fork出的子项目

### 10.2 添加Submodule

将Fork出的仓库作为Submodule添加到主项目：

```bash
# 添加个人Fork的仓库作为子模块
git submodule add https://gitlab.com/username/forked-repo.git path/to/submodule

# 提交子模块添加
git add .gitmodules path/to/submodule
git commit -m "chore: 添加子模块"
```

### 10.3 克隆包含Submodule的项目

```bash
# 克隆主项目
git clone https://gitlab.com/organization/main-project.git

# 初始化并更新所有子模块
cd main-project
git submodule update --init --recursive
```

### 10.4 更新Submodule

当Fork仓库有更新并合并到主仓库后，更新子模块：

```bash
# 进入子模块目录
cd path/to/submodule

# 获取最新更改
git fetch origin
git checkout main
git pull

# 返回主项目并提交子模块更新
cd ../
git add path/to/submodule
git commit -m "chore: 更新子模块"
```

### 10.5 Submodule管理最佳实践

- 在`.gitmodules`文件中使用HTTPS URL而非SSH URL，便于所有人访问
- 指定子模块的具体分支或标签，避免使用默认的HEAD
- 定期更新子模块到最新版本
- 在CI/CD流程中包含子模块的检查和更新步骤
- 使用`git submodule foreach`命令批量操作多个子模块

### 10.6 Submodule与Fork工作流结合

在我们的项目中，Submodule与Fork工作流结合使用的标准流程如下：

1. 项目主仓库包含多个子模块，每个子模块对应一个独立的功能模块
2. 开发者从主仓库和相关子模块仓库分别Fork出个人仓库
3. 在个人Fork的子模块仓库中进行开发
4. 通过MR将子模块的更改合并到子模块主仓库
5. 更新主项目中的子模块引用，指向最新的子模块版本
6. 提交主项目的更改，完成整体功能的更新

### 10.7 顶层集成仓库管理

#### 10.7.1 集成仓库概念

- **顶层集成仓库**：用于整体项目的集成与CI/CD流程管理的特殊仓库
- **独立性原则**：顶层仓库不直接开发业务代码，仅负责集成管理
- **统一管理**：CI/CD流程、自动化集成、统一发布等操作均在顶层仓库进行

#### 10.7.2 集成仓库权限管理

- **项目负责人**：具备顶层集成仓库的Maintainer权限
- **模块负责人**：具备各自子仓库的Maintainer权限，顶层仓库为Reporter权限
- **普通开发者**：所有仓库均为Reporter权限

#### 10.7.3 集成更新流程

1. 各模块在独立仓库中完成开发和测试
2. 模块负责人通过MR方式向顶层仓库提交依赖关系更新
3. MR必须关联项目里程碑（milestone）
4. 项目负责人审核MR，关注里程碑完成情况、CI状态、测试报告等
5. 审核通过后合并，触发顶层CI流程
6. 实现自动化集成与发布

## 11. Git LFS管理规范

继续学习高级特性，Git LFS（Large File Storage）是处理大文件的专门工具，对于包含设计文件、媒体资源的项目非常重要。

### 11.1 Git LFS简介

Git LFS（Large File Storage）是Git的扩展，用于高效管理大型文件。它通过将大文件存储在单独的服务器上，并在Git仓库中只保留指向这些文件的轻量级指针，从而解决了Git在处理大文件时的性能问题。

### 11.2 适用场景

Git LFS适用于以下场景：

- 需要版本控制的大型二进制文件（如图像、视频、音频等）
- 大型数据集文件
- 编译后的二进制文件
- 游戏开发中的资源文件
- 设计文件（如PSD、AI文件等）

### 11.3 安装与配置

#### 11.3.1 安装Git LFS

```bash
# Windows (使用Chocolatey)
choco install git-lfs

# macOS (使用Homebrew)
brew install git-lfs

# Linux (Debian/Ubuntu)
sudo apt-get install git-lfs

# 在所有平台上，安装后需要设置Git LFS
git lfs install
```

#### 11.3.2 在项目中启用Git LFS

```bash
# 进入项目目录
cd your-repository

# 初始化Git LFS
git lfs install

# 跟踪特定文件类型
git lfs track "*.psd"
git lfs track "*.zip"
git lfs track "*.mp4"

# 确保.gitattributes文件被提交
git add .gitattributes
git commit -m "chore: 配置Git LFS跟踪大文件"
```

### 11.4 使用Git LFS的工作流程

#### 11.4.1 跟踪文件

```bash
# 跟踪特定文件类型
git lfs track "*.extension"

# 跟踪特定文件
git lfs track "path/to/file.bin"

# 查看当前跟踪的文件模式
git lfs track
```

#### 11.4.2 常规Git操作

启用Git LFS后，可以使用常规的Git命令进行操作：

```bash
# 添加文件
git add large-file.psd

# 提交更改
git commit -m "feat: 添加设计文件"

# 推送到远程仓库
git push origin main
```

Git LFS会自动处理被跟踪的大文件，将它们上传到LFS存储服务器。

#### 11.4.3 克隆包含LFS文件的仓库

```bash
# 克隆仓库
git clone https://gitlab.com/organization/project-name.git

# 拉取LFS文件（如果没有自动拉取）
git lfs pull
```

### 11.5 GitLab中的LFS配置

#### 11.5.1 启用GitLab LFS

在GitLab项目中启用LFS：

1. 进入项目设置 > 常规 > 可见性、项目功能、权限
2. 在"仓库"部分，确保"Git LFS"选项已启用
3. 保存更改

#### 11.5.2 LFS存储限制

注意GitLab实例的LFS存储限制：

- GitLab.com：每个仓库默认有10GB的LFS存储空间
- 自托管GitLab：由管理员设置的限制

超出限制时，需要联系管理员增加配额或清理不必要的LFS对象。

### 11.6 Git LFS最佳实践

1. **选择性跟踪**：只对确实需要版本控制的大文件使用LFS
2. **使用.gitignore**：对不需要版本控制的大文件使用.gitignore
3. **定期清理**：使用`git lfs prune`清理不再需要的本地LFS缓存
4. **批量操作**：使用`git lfs migrate`将现有文件批量转换为LFS对象
5. **带宽考虑**：在带宽有限的环境中，使用`git lfs fetch --recent`只获取最近的LFS对象
6. **CI/CD配置**：在CI/CD流程中配置适当的LFS拉取策略，避免不必要的下载

### 11.7 常见LFS问题与解决方案

#### 11.7.1 LFS文件未被正确跟踪

**问题**：添加的大文件没有被LFS跟踪

**解决方案**：
1. 确保已运行`git lfs install`
2. 检查`.gitattributes`文件中是否正确配置了文件模式
3. 如果文件已经提交，使用`git lfs migrate`转换

#### 11.7.2 LFS文件下载失败

**问题**：克隆或拉取时LFS文件下载失败

**解决方案**：
1. 检查网络连接
2. 验证GitLab LFS服务器是否可访问
3. 确认用户有权访问LFS对象
4. 尝试使用`git lfs fetch --all`手动获取所有LFS对象

## 12. CI/CD集成规范

进入高级特性的最后部分，CI/CD集成是现代软件开发的重要组成部分，确保代码质量和自动化部署。

### 12.1 CI触发机制

#### 12.1.1 自动触发

- **MR触发**：项目代码仓库的Merge Request会自动触发CI流程
- **合并前置条件**：CI通过后才允许合并MR
- **分层CI**：项目代码仓库CI与顶层仓库CI相互独立，互不干扰

#### 12.1.2 CI内容要求

项目代码仓库的CI配置应至少包含：

- **单元测试**：确保代码功能正确性
- **代码规范审核**：检查代码风格和规范
- **安全扫描**：检测潜在的安全漏洞
- **构建验证**：确保代码可以正常构建

### 12.2 CI配置管理

#### 12.2.1 配置文件管理

- **版本控制**：CI配置文件（如`.gitlab-ci.yml`）必须纳入版本控制
- **变更流程**：CI流程的变更必须通过Merge Request提交
- **同行评审**：CI配置变更需经过同行评审
- **最终审批**：由项目整体负责人执行合入，确保流程变更的规范性和安全性

#### 12.2.2 CI维护责任

- **模块负责人**：具备编辑和维护各自模块CI流程的能力
- **项目负责人**：负责顶层集成仓库的CI配置和整体CI策略
- **质量保证**：所有CI配置变更都需要经过测试验证

### 12.3 顶层集成CI

#### 12.3.1 集成CI职责

顶层仓库（Integration Repo）的CI仅负责：

- **多仓库集成测试**：验证各模块间的集成兼容性
- **系统级验证**：进行端到端的系统测试
- **自动化部署**：执行自动化部署流程
- **发布管理**：管理版本发布和标签创建

#### 12.3.2 集成CI流程

1. 顶层仓库MR合并后触发集成CI
2. 拉取所有子模块的最新代码
3. 执行集成测试和系统验证
4. 生成集成报告和测试结果
5. 如果测试通过，执行自动化部署
6. 更新发布状态和通知相关人员

## 13. 里程碑与发版管理

### 13.1 里程碑管理

#### 13.1.1 里程碑定义

- **里程碑概念**：项目开发过程中的重要节点，通常对应版本发布或重要功能完成
- **里程碑分类**：
  - **版本里程碑**：对应软件版本发布（如v1.0.0、v1.1.0）
  - **功能里程碑**：对应重要功能模块完成
  - **阶段里程碑**：对应项目开发阶段完成（如Alpha、Beta、RC）

#### 13.1.2 里程碑创建与管理

- **创建权限**：由项目负责人创建和管理里程碑
- **命名规范**：使用清晰的命名规范，如"v1.2.0-Release"、"Feature-UserAuth"
- **时间规划**：设置合理的开始时间和截止时间
- **描述要求**：详细描述里程碑的目标和验收标准

### 13.2 MR与里程碑关联

#### 13.2.1 强制关联要求

- **顶层仓库MR**：向顶层集成仓库提交的MR必须关联项目里程碑
- **依赖更新MR**：更新submodule或依赖关系的MR必须关联里程碑
- **关联方式**：在MR创建时选择对应的里程碑，或在MR描述中明确标注

#### 13.2.2 关联作用

- **进度跟踪**：通过里程碑跟踪项目整体进度
- **发版控制**：确保只有完成里程碑要求的功能才能发版
- **质量保证**：通过里程碑管理确保发版质量

### 13.3 发版评审流程

#### 13.3.1 发版前评审

项目负责人在审核顶层仓库MR时需要关注：

- **里程碑完成情况**：检查关联的里程碑是否已完成
- **CI状态**：确保所有CI检查都已通过
- **测试报告**：审查测试结果和覆盖率报告
- **发版评审记录**：检查是否有相关的发版评审文档

#### 13.3.2 特殊发版流程

当存在严重Bug但需要紧急发版时：

1. **问题评估**：评估Bug的严重程度和影响范围
2. **特殊发版评审**：启动特殊发版评审流程
3. **风险评估**：评估发版风险和应对措施
4. **评审记录**：将评审过程和决策记录到项目Wiki
5. **发版执行**：在评审通过后执行发版
6. **后续跟踪**：发版后持续跟踪问题解决情况

### 13.4 发版管理最佳实践

#### 13.4.1 发版计划

- **定期发版**：建立定期发版节奏，如每两周一个小版本
- **功能冻结**：在发版前设置功能冻结期，只允许Bug修复
- **回归测试**：发版前进行充分的回归测试
- **发版通知**：及时通知相关团队发版计划和变更内容

#### 13.4.2 发版后管理

- **版本标签**：为每个发版创建对应的Git标签
- **发版说明**：编写详细的发版说明文档
- **问题跟踪**：跟踪发版后的问题反馈和解决情况
- **经验总结**：定期总结发版过程中的经验和改进点

#### 13.4.3 回滚策略

- **回滚准备**：为每个发版准备回滚方案
- **快速回滚**：建立快速回滚机制，应对紧急情况
- **数据保护**：确保回滚过程中的数据安全
- **通知机制**：建立回滚通知机制，及时通知相关人员

## 14. 常见问题与解决方案

进入实用工具部分，这里汇总了在使用Git和GitLab过程中经常遇到的问题及其解决方案。

### 14.1 合并冲突

**问题**：合并分支或提交MR时出现冲突

**解决方案**：
1. 先拉取最新的目标分支代码
2. 在本地解决冲突
3. 提交解决冲突的更改
4. 重新推送到远程仓库

### 14.2 误提交敏感信息

**问题**：不小心提交了敏感信息（如密码、密钥）

**解决方案**：
1. 使用 `git filter-branch` 或 BFG Repo-Cleaner 工具从历史记录中删除敏感信息
2. 更新 `.gitignore` 文件，避免再次提交
3. 强制推送到远程仓库（需谨慎操作）

### 14.3 大文件处理

**问题**：需要管理大文件

**解决方案**：
1. 使用 Git LFS（Large File Storage）管理大文件，详见[11. Git LFS管理规范](#11-git-lfs管理规范)
2. 配置 `.gitattributes` 文件，指定需要用LFS管理的文件类型
3. 使用 `git lfs track` 命令跟踪大文件

### 14.4 Fork仓库与上游仓库不同步

**问题**：个人Fork的仓库与上游仓库差异过大，难以合并

**解决方案**：
1. 备份本地未提交的更改
2. 重置个人Fork仓库的主分支，与上游仓库同步：
   ```bash
   git fetch upstream
   git checkout main
   git reset --hard upstream/main
   git push origin main --force
   ```
3. 在最新的主分支基础上创建新的功能分支

### 14.5 Submodule更新问题

**问题**：子模块更新后，其他团队成员无法获取最新的子模块代码

**解决方案**：
1. 确保主项目已提交子模块的更新
2. 其他团队成员执行以下命令：
   ```bash
   git pull
   git submodule update --init --recursive
   ```
3. 如果子模块有自己的分支，可能需要在子模块目录中切换到正确的分支

### 14.6 Git常用命令速查

#### 14.6.1 分支操作

```bash
# 查看所有分支
git branch -a

# 创建新分支
git checkout -b feature/new-feature

# 切换分支
git checkout develop

# 删除本地分支
git branch -d feature/old-feature

# 删除远程分支
git push origin --delete feature/old-feature
```

#### 14.6.2 提交操作

```bash
# 查看状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "feat: 添加新功能"

# 修改最后一次提交
git commit --amend

# 推送到远程
git push origin feature/new-feature
```

#### 14.6.3 Fork与上游仓库操作

```bash
# 添加上游仓库
git remote add upstream https://gitlab.com/organization/project-name.git

# 查看远程仓库
git remote -v

# 获取上游仓库更新
git fetch upstream

# 合并上游更新
git merge upstream/main

# 推送到个人Fork仓库
git push origin main
```

#### 14.6.4 Submodule操作

```bash
# 添加子模块
git submodule add <repository_url> <path>

# 初始化子模块
git submodule init

# 更新子模块
git submodule update

# 初始化并更新所有子模块（包括嵌套的子模块）
git submodule update --init --recursive

# 对所有子模块执行命令
git submodule foreach 'git pull origin main'

# 删除子模块
git submodule deinit -f -- <path_to_submodule>
rm -rf .git/modules/<path_to_submodule>
git rm -f <path_to_submodule>
```

#### 14.6.5 Git LFS操作

```bash
# 安装Git LFS
git lfs install

# 跟踪特定文件类型
git lfs track "*.psd"
git lfs track "*.zip"
git lfs track "*.mp4"

# 查看当前跟踪的文件模式
git lfs track

# 查看当前被LFS跟踪的文件
git lfs ls-files

# 拉取LFS文件
git lfs pull

# 只拉取最近使用的LFS文件
git lfs fetch --recent

# 拉取所有LFS文件
git lfs fetch --all

# 检查LFS文件
git lfs status

# 清理本地LFS缓存
git lfs prune

# 将现有文件转换为LFS对象
git lfs migrate import --include="*.psd" --everything

# 查看LFS锁
git lfs locks

# 锁定文件（防止他人修改）
git lfs lock <file>

# 解锁文件
git lfs unlock <file>
```
