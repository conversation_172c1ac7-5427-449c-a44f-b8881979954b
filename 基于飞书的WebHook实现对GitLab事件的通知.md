# 基于飞书的webhook实现对GitLab事件的通知

## **1. 功能概述**

通过GitLab的Webhook功能，将代码仓库事件（如推送、合并请求、流水线状态等）实时推送到飞书群聊或机器人，实现团队协作的即时通知。

**适用场景**：

- 代码提交/合并请求通知
- CI/CD流水线成功/失败提醒
- issue创建/修改提醒

## **2. 前置条件**

1. **权限要求**：
   - GitLab仓库的**管理员权限**（用于配置Webhook）
   - 飞书群组的**管理权限**（用于添加机器人）
2. **工具准备**：
   - GitLab仓库地址
   - 飞书账号及目标群聊

## **3. 配置流程**

 **3.1 GitLab端：配置Webhook**

1. 创建Webhook。
   - 创建gitlab的webhooks，将上面飞书中的webhook的地址粘贴到URL里面，选择gitlab的触发的事件源。
   - 测试gitlab的webhooks，并将测试生成的请求json信息招贴到前面飞书的webhooks的参数中。
   - 查看webhooks的详细信息并将请求复制出来
   - 然后回到飞书的webhook创建流程的界面，将gitlab通过webhooks的issue触发信息粘贴到飞书的webhook的参数中，点击**完成**。

**3.2 飞书端：创建自定义机器人**

1. 创建机器人应用
   1. 在[飞书机器人助手](https://botbuilder.feishu.cn/home/<USER>
2. 创建流程
   1. 打开 [**机器人指令**](https://botbuilder.feishu.cn/home/<USER>
   2. 需要将webhook的地址回填到[gitlab](https://so.csdn.net/so/search?q=gitlab&spm=1001.2101.3001.7020)的webhook的url中，这里可以先记录下来。
3. 编辑流程
   1. 这里选择通过**通过官方机器人发消息**和**通过飞书机器人发消息**都可以，我们以**通过官方机器人发消息**为例。
   2. 选择要发送的消息和内容，这里可以通过变量控制会效果更好。
   3. ***\*点击\**完成**、对流程进行**命名**，然后**启动**

**附录**

- [飞书开放平台文档](https://open.feishu.cn/document/)
- [GitLab Webhook官方指南](https://docs.gitlab.com/ee/user/project/integrations/webhooks.html)