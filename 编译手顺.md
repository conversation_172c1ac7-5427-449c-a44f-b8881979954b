### 编译文件

以下是一个简单的 `CMakeLists.txt` 文件示例：

#### `CMakeLists.txt`

```cmake
cmake_minimum_required(VERSION 3.10)
project(MyKanziPlugin)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED True)

# Kanzi SDK 路径
set(KANZI_SDK_PATH "path/to/kanzi/sdk")

# 包含 Kanzi 头文件
include_directories(${KANZI_SDK_PATH}/include)

# 链接 Kanzi 库
link_directories(${KANZI_SDK_PATH}/lib)

# 源文件
set(SOURCES
    MyPlugin.cpp
    MyPlugin.hpp
    PluginInitialization.cpp
)

# 生成动态库
add_library(MyKanziPlugin SHARED ${SOURCES})

# 链接 Kanzi 库
target_link_libraries(MyKanziPlugin kanzi)
```

### 使用手册

#### 1. 设置开发环境

确保你已经安装了 Kanzi SDK，并且你的开发环境中有一个支持 C++ 的编译器和 CMake。

#### 2. 创建插件项目

创建一个新的目录结构，并将上述源文件和 `CMakeLists.txt` 文件放入其中。例如：

```
MyKanziPlugin/
├── CMakeLists.txt
├── MyPlugin.cpp
├── MyPlugin.hpp
└── PluginInitialization.cpp
```

#### 3. 编译插件

在终端中导航到项目目录并运行以下命令：

```sh
mkdir build
cd build
cmake ..
make
```

这将生成一个动态链接库（例如 `libMyKanziPlugin.so` 或 `MyKanziPlugin.dll`），具体取决于你的操作系统。

#### 4. 加载插件

将生成的库文件复制到你的 Kanzi 项目目录中。例如，将 `libMyKanziPlugin.so` 复制到 `KanziProject/plugins/` 目录。

#### 5. 在 Kanzi Studio 中使用插件

1. 打开 Kanzi Studio 并加载你的项目。
2. 在项目资源管理器中，右键点击 `Plugins` 文件夹并选择 `Add Plugin...`。
3. 选择你刚刚编译的插件库文件（例如 `libMyKanziPlugin.so` 或 `MyKanziPlugin.dll`）。
4. 在场景中添加一个新的节点，并将其类型设置为 `MyPlugin`。
5. 将你希望旋转的模型作为 `MyPlugin` 节点的子节点。

#### 6. 运行项目

运行你的 Kanzi 项目，触摸屏幕并移动手指，你应该会看到模型根据手指的移动进行旋转。

### 完整的项目结构

```
MyKanziPlugin/
├── CMakeLists.txt
├── MyPlugin.cpp
├── MyPlugin.hpp
├── PluginInitialization.cpp
├── build/  # 编译输出目录
└── KanziProject/  # 你的 Kanzi 项目目录
    ├── plugins/
    │   └── libMyKanziPlugin.so  # 编译生成的插件库文件
    └── ...  # 其他 Kanzi 项目文件
```