### GitLab搭建UE4自动化CI流程文档

------

#### **一、搭建步骤**

##### **1. 环境准备**

- **核心目标**：确保 GitLab Runner 和 Unreal Engine 环境可用。

- **详细步骤**：

  1. **安装 GitLab Runner（Windows）**：

     - 下载 [Windows 版 Runner](https://docs.gitlab.com/runner/install/windows.html)。

     - 以管理员身份安装并启动服务：

       ```powershell
       cd C:\GitLab-Runner
       .\gitlab-runner.exe install
       .\gitlab-runner.exe start
       ```

  2. **配置 Unreal Engine 4.27**：

     - 安装 UE4.27 并启用 Android 支持。
     - 配置 Android SDK/NDK/JDK（通过 UE Editor 的 `Project Settings → Android`）。

  3. **磁盘空间分配**：

     - 预留 150GB+ 空间（建议 Runner 工作目录挂载独立磁盘）。

##### **2. 注册并配置 GitLab Runner**

- **核心目标**：绑定 Runner 到 GitLab 项目并分配权限。

- **详细步骤**：

  1. **注册 Runner**：

     ```powershell
     .\gitlab-runner.exe register
     ```

     输入 GitLab URL、Token，选择 `shell` 执行器并标记为 `Windows`（与 YAML 中的 `tags` 匹配）。

  2. **权限配置**：

     - 确保 Runner 服务账户对 UE 引擎目录和项目路径有读写权限。

     - 修改 `config.toml` 提升超时限制：

       ```
       [[runners]]
         executor = "shell"
         [runners.cache]
           MaxUploadedArchiveSize = 0  # 禁用缓存大小限制
       ```

##### **3. 编写 `.gitlab-ci.yml`**

- **核心目标**：定义构建流程、缓存策略和产物收集。

- **YAML 示例**：

  ```yml
  variables:
    UE_PATH: "D:/UE4.24/unrealengine_4.27/Engine"
    PROJECT_ROOT: "C:/GitLab-Runner/builds/i2HPDqVQ/0/l946/ue4_only/CarLinx_L946_UE"
    UPKROJECT_FILE: "C:/GitLab-Runner/builds/i2HPDqVQ/0/l946/ue4_only/CarLinx_L946_UE/CarLinx_L946_UE.uproject"
    UE_EDITOR_CMD: "D:/UE4.24/unrealengine_4.27/Engine/Binaries/Win64/UE4Editor-Cmd.exe"
  
  stages:
    - build
  
  cache:
    paths:
      - $PROJECT_ROOT/Intermediate/  # 缓存引擎中间文件
      - $PROJECT_ROOT/Saved/        # 缓存已编译的引擎数据
  
  android-build:
    stage: build
    tags:
      - Windows-HeGe
    script: |
      # 设置 UTF-8 编码
      chcp 65001
  
      # 清理旧文件（仅清理 Android 相关目录，保留引擎缓存）
      $intermediateAndroidPath = "$PROJECT_ROOT/Intermediate/Android"
      $stagedBuildsAndroidPath = "$PROJECT_ROOT/Saved/StagedBuilds/Android"
      $binariesAndroidPath = "$PROJECT_ROOT/Binaries/Android"
  
      Remove-Item -Recurse -Force $intermediateAndroidPath -ErrorAction SilentlyContinue
      Remove-Item -Recurse -Force $stagedBuildsAndroidPath -ErrorAction SilentlyContinue
      Remove-Item -Recurse -Force $binariesAndroidPath -ErrorAction SilentlyContinue
  
      # 生成项目文件（仅在首次或项目文件变化时触发）
      & "$UE_PATH/Build/BatchFiles/GenerateProjectFiles.bat" "C:/GitLab-Runner/builds/i2HPDqVQ/0/l946/ue4_only/CarLinx_L946_UE/CarLinx_L946_UE.uproject" -Android
  
      # 编译并打包（保留引擎缓存，仅编译项目变更部分）
      & "$UE_PATH/Build/BatchFiles/RunUAT.bat" BuildCookRun `
          -project="C:/GitLab-Runner/builds/i2HPDqVQ/0/l946/ue4_only/CarLinx_L946_UE/CarLinx_L946_UE.uproject" `
          -platform=Android `
          -clientconfig=Shipping `
          -arch=armv7 `
          -build `
          -cook `
          -stage `
          -pak `
          -package `
          -compressed `
          -SkipOBBInAPK=true `
          -ue4exe="$UE_EDITOR_CMD"
  
      # 强制创建输出目录（确保路径存在）
      New-Item -Path $binariesAndroidPath -ItemType Directory -Force
  
      # 验证 APK 生成路径
      Write-Host "APK 生成目录内容:"
      Get-ChildItem -Path $binariesAndroidPath -Recurse
  
    artifacts:
      paths:
        - "$PROJECT_ROOT/Binaries/Android/*.apk"  # 修正路径为实际 APK 生成位置
      expire_in: 1 week
  
  
  ```

##### **4. 首次构建与验证**

- **核心目标**：触发 Pipeline 并确保流程完整。
- **详细步骤**：
  1. 提交 YAML 文件到仓库，选择 GitLab → `CI/CD → 流水线`。
  2. **故障排查**：
     - **路径错误**：检查 `UE_PATH` 和 `PROJECT_ROOT` 是否有效。
  3. **验证制品**：
     - 下载生成的 APK 并手动安装测试。

------

#### **二、YAML 文件存在的问题及优化方案**

##### **1. 路径硬编码问题**

- **问题**：所有路径均为绝对路径（如 `D:/UE4.24`），导致可移植性差。
- **优化方案**：
  - 使用环境变量（如 `%UE_ROOT%`）或 GitLab CI/CD 变量代替硬编码路径。
  - 通过 `$CI_PROJECT_DIR` 引用项目根目录，避免路径拼接错误。

##### **2. 缓存策略问题**

- **问题**：
  - 缓存 `Intermediate/` 和 `Saved/` 目录可能导致缓存爆炸（UE 中间文件通常很大）。
  - 未区分不同平台的缓存（如 Android 和 iOS 的中间文件可能冲突）。
- **优化方案**：
  - 仅缓存 `Saved/StagedBuilds/Android` 等必要目录。
  - 为缓存添加唯一键（如 `$CI_COMMIT_REF_SLUG`），避免多分支/任务干扰。

##### **3. 冗余构建步骤**

- **问题**：
  - `GenerateProjectFiles.bat` 每次都会执行，即使项目文件未变化。
  - 未利用增量编译（`-SkipBuild` 或 `-Iterate` 参数）。
- **优化方案**：
  - 添加条件判断，仅在 `.uproject` 或 `.Build.cs` 文件变更时生成项目文件。
  - 使用 `-iteratetarget` 参数加速后续编译。

##### **4. Android 构建参数问题**

- **问题**：
  - `-arch=armv7` 可能不兼容现代设备（推荐 `arm64`）。
  - 未配置签名密钥（APK 无法直接安装）。
- **优化方案**：
  - 添加 `-signapk` 参数并引用 GitLab 安全变量存储密钥。
  - 根据目标设备动态选择架构（如通过变量传递 `$ANDROID_ARCH`）。

 **5. 制品收集问题**

- **问题**：
  - APK 可能未生成在 `Binaries/Android` 目录（默认在 `Saved/StagedBuilds/Android`）。
  - 制品过期时间过短（`expire_in: 1 week`）。
- **优化方案**：
  - 修正制品路径为 `$PROJECT_ROOT/Saved/StagedBuilds/Android/*.apk`。
  - 将产物上传至仓库中的 `软件包与镜像库` 中

------

#### **三、搭建过程中遇到的问题**

##### **1. 环境配置问题**

- **Android 工具链缺失**：未安装 SDK/NDK 或版本不匹配（需与 UE 版本兼容）。
- **权限不足**：GitLab Runner 无权访问 UE 引擎目录（需以管理员身份运行服务）。

##### **2. 构建脚本问题**

- **路径空格转义失败**：路径中的空格（如 `Program Files`）需用引号包裹。
- **PowerShell 编码问题**：中文字符显示乱码，需要改为UTF-8的编码格式。

##### **3. 缓存与性能问题**

- **缓存失效**：中间文件频繁变动导致缓存频繁上传/下载（需调整缓存策略）。
- **构建超时**：首次全量编译耗时过长（需拆分任务或提高 Runner 配置）。

##### **4. 平台兼容性问题**

- **架构不匹配**：`armv7` APK 无法在部分设备安装（需适配 `arm64`）。
- **UE 版本冲突**：本地引擎版本与 CI 不一致（建议使用官方 Docker 镜像）。