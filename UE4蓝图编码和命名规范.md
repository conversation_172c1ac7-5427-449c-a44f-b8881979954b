# UE4 蓝图编码和命名规范

## 目录

- [1. 概述](#1-概述)
- [2. 命名约定](#2-命名约定)
- [3. 蓝图结构规范](#3-蓝图结构规范)
- [4. 变量规范](#4-变量规范)
- [5. 函数规范](#5-函数规范)
- [6. 事件规范](#6-事件规范)
- [7. 注释规范](#7-注释规范)
- [8. 文件夹组织规范](#8-文件夹组织规范)
- [9. 性能优化规范](#9-性能优化规范)
- [10. 版本控制规范](#10-版本控制规范)

## 1. 概述

本文档基于Epic Games官方编码标准，为UE4蓝图开发制定统一的编码和命名规范，旨在提高代码可读性、可维护性和团队协作效率。

### 1.1 适用范围

- 所有UE4蓝图类（Blueprint Class）
- 蓝图接口（Blueprint Interface）
- 蓝图函数库（Blueprint Function Library）
- 蓝图宏库（Blueprint Macro Library）
- 数据表（Data Table）
- 枚举（Enum）
- 结构体（Struct）

### 1.2 基本原则

- **一致性**：整个项目保持统一的命名和编码风格
- **可读性**：代码应该自解释，易于理解
- **可维护性**：便于后续修改和扩展
- **性能优先**：遵循UE4性能最佳实践

## 2. 命名约定

### 2.1 资源前缀规范

所有蓝图资源必须使用标准前缀，遵循Epic Games官方约定：

| 资源类型                   | 前缀   | 示例                 |
| -------------------------- | ------ | -------------------- |
| Blueprint Class            | BP_    | BP_PlayerCharacter   |
| Blueprint Interface        | BPI_   | BPI_Interactable     |
| Blueprint Function Library | BPL_   | BPL_MathUtils        |
| Blueprint Macro Library    | BPM_   | BPM_CommonMacros     |
| Widget Blueprint           | WBP_   | WBP_MainMenu         |
| Animation Blueprint        | ABP_   | ABP_Character        |
| AI Behavior Tree           | BT_    | BT_EnemyAI           |
| AI Blackboard              | BB_    | BB_EnemyData         |
| Data Table                 | DT_    | DT_WeaponStats       |
| Enum                       | E_     | E_WeaponType         |
| Struct                     | S_     | S_PlayerData         |
| Curve                      | Curve_ | Curve_DamageOverTime |

### 2.2 命名格式规范

#### 2.2.1 PascalCase（帕斯卡命名法）

用于：类名、函数名、事件名、枚举值

```
正确示例：
- BP_PlayerCharacter
- GetPlayerHealth
- OnPlayerDeath
- E_WeaponType::Rifle

错误示例：
- bp_playercharacter
- getPlayerHealth
- onPlayerDeath
- e_weapontype::rifle
```

#### 2.2.2 camelCase（驼峰命名法）

用于：变量名、参数名

```
正确示例：
- playerHealth
- maxAmmoCount
- isPlayerAlive

错误示例：
- PlayerHealth
- max_ammo_count
- IsPlayerAlive
```

#### 2.2.3 UPPER_CASE（全大写）

用于：常量、宏定义

```
正确示例：
- MAX_PLAYER_COUNT
- DEFAULT_HEALTH_VALUE

错误示例：
- max_player_count
- DefaultHealthValue
```

### 2.3 布尔变量命名规范

布尔变量应使用明确的前缀，表明其状态：

```
推荐前缀：
- is/Is: isAlive, isVisible, isEnabled
- has/Has: hasWeapon, hasKey, hasPermission
- can/Can: canJump, canShoot, canInteract
- should/Should: shouldRespawn, shouldSave

避免使用：
- enabled (使用 isEnabled)
- visible (使用 isVisible)
- dead (使用 isDead)
```

## 3. 蓝图结构规范

### 3.1 蓝图组织结构

每个蓝图应按以下顺序组织节点：

1. **变量声明区域**

   - 公共变量（Public）
   - 受保护变量（Protected）
   - 私有变量（Private）
2. **函数声明区域**

   - 公共函数
   - 受保护函数
   - 私有函数
3. **事件处理区域**

   - 生命周期事件
   - 输入事件
   - 自定义事件

### 3.2 变量分组规范

使用Category对变量进行逻辑分组：

```
推荐分组：
- "Config" - 配置相关变量
- "Runtime" - 运行时变量
- "References" - 对象引用
- "UI" - 用户界面相关
- "Audio" - 音频相关
- "Effects" - 特效相关
- "Debug" - 调试相关
```

### 3.3 函数分组规范

```
推荐分组：
- "Initialization" - 初始化函数
- "Gameplay" - 游戏逻辑函数
- "Movement" - 移动相关函数
- "Combat" - 战斗相关函数
- "UI" - 界面相关函数
- "Utilities" - 工具函数
- "Events" - 事件处理函数
```

## 4. 变量规范

### 4.1 变量声明规范

```
✅ 正确示例：
[Category = "Config"]
[Tooltip = "玩家的最大生命值"]
[Meta = (ClampMin = "1", ClampMax = "1000")]
float maxHealth = 100.0f;

[Category = "Runtime"]
[Tooltip = "当前生命值"]
float currentHealth;

❌ 错误示例：
float health; // 缺少分类和说明
int hp; // 命名不清晰
```

### 4.2 变量可见性规范

- **Public**: 需要在编辑器中调整或被其他蓝图访问的变量
- **Protected**: 仅在继承类中使用的变量
- **Private**: 仅在当前蓝图内部使用的变量

### 4.3 变量初始化规范

- 所有变量必须设置合理的默认值
- 使用Meta标签设置数值范围限制
- 为每个变量添加清晰的Tooltip说明

## 5. 函数规范

### 5.1 函数命名规范

```
✅ 正确示例：
- GetPlayerHealth() - 获取数据
- SetPlayerHealth(float newHealth) - 设置数据
- CalculateDamage(float baseDamage) - 计算逻辑
- OnPlayerDeath() - 事件处理
- CanPlayerJump() - 条件判断

❌ 错误示例：
- health() - 不明确的动作
- damage() - 缺少动词
- player() - 过于宽泛
```

### 5.2 函数参数规范

- 输入参数使用camelCase命名
- 输出参数使用PascalCase命名
- 为所有参数添加Tooltip说明
- 使用合适的参数类型，避免过度使用Object引用

```
✅ 正确示例：
Function: CalculateDamage
Input: baseDamage (float) - "基础伤害值"
Input: damageMultiplier (float) - "伤害倍数"
Output: FinalDamage (float) - "最终伤害值"
```

### 5.3 函数复杂度控制

- 单个函数不应超过50个节点
- 复杂逻辑应拆分为多个子函数
- 避免深层嵌套，最多3层条件嵌套

## 6. 事件规范

### 6.1 事件命名规范

```
生命周期事件：
- Event BeginPlay
- Event EndPlay
- Event Tick

输入事件：
- InputAction_Jump
- InputAxis_MoveForward

自定义事件：
- OnPlayerDeath
- OnWeaponEquipped
- OnLevelCompleted
```

### 6.2 事件绑定规范

- 在BeginPlay中进行事件绑定
- 在EndPlay中解除事件绑定
- 避免在Tick中进行频繁的事件检查

## 7. 注释规范

### 7.1 蓝图注释

- 每个蓝图必须包含类级别的注释说明
- 复杂的逻辑块必须添加注释节点
- 使用颜色区分不同功能模块

### 7.2 注释内容规范

```
✅ 良好的注释：
"计算玩家受到伤害后的最终生命值，考虑护甲减免"
"检查玩家是否满足跳跃条件（在地面上且未处于眩晕状态）"

❌ 无用的注释：
"设置变量" 
"调用函数"
"如果条件为真"
```

### 7.3 TODO注释规范

```
格式：TODO: [优先级] 描述 - 负责人 - 日期

示例：
TODO: [HIGH] 优化AI寻路算法性能 - 张三 - 2024-01-15
TODO: [LOW] 添加音效反馈 - 李四 - 2024-01-20
```

## 8. 文件夹组织规范

### 8.1 项目目录结构

```
Content/
├── Blueprints/
│   ├── Characters/          # 角色相关蓝图
│   │   ├── Player/         # 玩家角色
│   │   ├── NPCs/           # NPC角色
│   │   └── Enemies/        # 敌人角色
│   ├── Weapons/            # 武器系统
│   ├── Items/              # 道具系统
│   ├── UI/                 # 用户界面
│   │   ├── Menus/          # 菜单界面
│   │   ├── HUD/            # 游戏内UI
│   │   └── Widgets/        # 通用组件
│   ├── GameModes/          # 游戏模式
│   ├── Controllers/        # 控制器
│   ├── Components/         # 组件
│   └── Utilities/          # 工具类
├── Data/
│   ├── DataTables/         # 数据表
│   ├── Curves/             # 曲线资源
│   └── Enums/              # 枚举定义
├── Interfaces/             # 蓝图接口
├── Libraries/              # 函数库
└── Macros/                 # 宏库
```

### 8.2 命名层级规范

```
层级1: 功能模块 (Characters, Weapons, UI)
层级2: 子系统 (Player, Enemies, Menus)
层级3: 具体实现 (BP_PlayerCharacter, BP_EnemyGoblin)

示例路径：
/Content/Blueprints/Characters/Player/BP_PlayerCharacter
/Content/Blueprints/Weapons/Rifles/BP_AssaultRifle
/Content/Blueprints/UI/Menus/WBP_MainMenu
```

### 8.3 资源引用规范

- 避免跨模块的直接引用
- 使用接口或事件系统进行模块间通信
- 将共享资源放在Utilities或Libraries文件夹中

## 9. 性能优化规范

### 9.1 Tick优化规范

```
❌ 避免在Tick中执行：
- 复杂的数学计算
- 频繁的碰撞检测
- 大量的对象查找
- 字符串操作

✅ 推荐做法：
- 使用Timer替代Tick
- 缓存频繁访问的对象引用
- 使用事件驱动而非轮询
- 合理使用对象池
```

### 9.2 内存管理规范

```
✅ 良好实践：
- 及时清理不需要的对象引用
- 使用Soft Reference避免循环引用
- 在EndPlay中清理Timer和事件绑定
- 合理使用Garbage Collection

❌ 避免做法：
- 在循环中创建大量临时对象
- 持有不必要的Hard Reference
- 忘记清理事件监听器
```

### 9.3 蓝图编译优化

- 避免使用过多的Cast节点
- 使用Interface替代多重继承
- 减少蓝图间的循环依赖
- 合理使用Blueprint Nativization

### 9.4 渲染性能规范

```
UI优化：
- 使用Invalidation Box优化UI重绘
- 避免在Tick中更新UI元素
- 合理使用Widget的Visibility设置
- 使用Object Pool管理动态UI元素

材质优化：
- 使用Material Parameter Collection
- 避免在蓝图中频繁修改材质参数
- 使用Dynamic Material Instance
```

## 10. 版本控制规范

### 10.1 提交规范

```
提交信息格式：
[类型] 简短描述

类型标识：
- feat: 新功能
- fix: 修复bug
- refactor: 重构代码
- perf: 性能优化
- docs: 文档更新
- style: 代码格式调整

示例：
feat: 添加玩家跳跃功能
fix: 修复武器切换时的动画问题
refactor: 重构AI行为树结构
```

### 10.2 分支管理规范

```
分支命名：
- main/master: 主分支
- develop: 开发分支
- feature/功能名: 功能分支
- hotfix/修复名: 热修复分支

示例：
feature/player-inventory-system
hotfix/weapon-damage-calculation
```

### 10.3 蓝图版本控制注意事项

- 避免同时修改同一个蓝图文件
- 使用蓝图Diff工具检查变更
- 重要修改前创建备份
- 定期清理无用的蓝图资源

## 11. 代码审查规范

### 11.1 审查检查清单

**命名规范检查：**

- [ ] 资源前缀是否正确
- [ ] 命名是否清晰易懂
- [ ] 变量命名是否遵循camelCase
- [ ] 函数命名是否遵循PascalCase

**结构规范检查：**

- [ ] 变量是否正确分组
- [ ] 函数复杂度是否合理
- [ ] 是否有适当的注释
- [ ] 代码逻辑是否清晰

**性能规范检查：**

- [ ] 是否避免了不必要的Tick使用
- [ ] 是否正确管理对象引用
- [ ] 是否有内存泄漏风险
- [ ] 渲染调用是否优化

### 11.2 常见问题及解决方案

| 问题         | 解决方案                       |
| ------------ | ------------------------------ |
| 蓝图过于复杂 | 拆分为多个子函数或组件         |
| 性能问题     | 使用Profiler分析，优化热点代码 |
| 命名不规范   | 使用重构工具批量重命名         |
| 缺少注释     | 补充必要的功能说明             |

## 12. 总结

遵循本规范可以：

- 提高代码可读性和可维护性
- 减少团队协作中的沟通成本
- 降低项目维护难度
- 提升整体开发效率
- 确保项目的长期稳定性

**重要提醒：**

- 规范需要团队全员遵守
- 定期review和更新规范内容
- 新成员入职时进行规范培训
- 在代码审查中严格执行规范要求

---

**文档版本：** v1.0
**最后更新：** 2024年1月
**维护者：** 开发团队
**审核者：** 技术负责人
