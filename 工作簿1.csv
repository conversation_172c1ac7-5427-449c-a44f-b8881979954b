﻿提交时间,今日总结,项目工作汇报,明日计划
2025-05-27 19:40,"1.对车凌输出OTA1的修改产物。
2.参加datasource的代码评审会议。
3.整理黑卡死问题的测试结论。","1.打包提交OTA1文言修改的产物给车凌仓库。
2.Android侧datasource多线程发送信号，未出现墓碑文件，但存在1份anr，在量产工程中已修改，暂未复现。","1.整理黑卡死下一步测试需要用到的资产。
2.持续跟踪黑卡死问题。"
2025-05-27 09:20,"1.修改OTA1的文言变更需求，并输出测试apk。
commit-id：7e489f2d7498f828ab71dd4493500325b4ccbb34
2.修改黑卡死Android工程，增加多线程发送信号到UE。
软件包库 · L946 / Package · GitLab","1.修改APA/HPA/LSDA中提到的OTA1的文言变更，本地测试未发现问题，已经打包编译apk并输出给车凌测试。
2.针对黑卡死问题，增加Android工程的多线程发送逻辑，本地平板测试。","1.提交OTA1的变更产物给车凌仓库。
2.继续跟踪黑卡死测试项。
3.准备时钟问题测试需要的资产。"
2025-05-23 19:16,"1.开车头车尾功能需求澄清会议。
2.修改车头车尾联调提出的问题。
3.优化GitLab分支与代码提交规范内容。
4.修改datasource模拟发送脚本。
5.同康哥评审Git管理流程。","1.车头车尾需求澄清后，吉利提出了需求变更，联调受阻，待德赛释放DHU修改后的版本再进行联调。
2.黑卡死问题，修改datasource模拟发送脚本，提高线程通道数量。","1.继续跟踪黑卡死问题，执行下一步制定的行动项。
2.整理输出946的模块设计文档。"
2025-05-22 20:19,"1.更新黑卡死问题文档。
2.someip脚本增加多线程发送功能。
3.分析车头车尾功能问题。
4.参加datasource代码评审会。
5.参加首次技术分享会。","1.黑卡死问题，增加了someip发送频率，未出现墓碑文件，下一步，增加datasource的多线程发送。
2.车头车尾泊入提出了两个问题，目前已经修复，明日输出apk。","1.分析jira中车位问题。
2.评审gitlab管理流程。
3.持续跟踪黑卡死问题。"
2025-05-21 19:49,"1.车头车尾泊入Apk已经准备完毕，待明天释放给吉利。
2.提交LSDA修改后的产物到车凌仓库。
3.输出GitLab分支与代码提交规范文档。
4.整理L946 泊车进程崩溃问题分析与改进报告，加入下一步行动项。
5.修改Some IP随机值发送报错问题。
6.整理李凌涛交接文档交接整理-llt。","1.打包编译联调车头车尾泊入的apk，已经准备好了，明天释放给车凌。
2.整理黑卡死最近遇到的问题，并且制定下一步行动项，","1.梳理L946项目中的文档，从APA模块的概要设计和详细设计开始。
2.对外输出联调APP。
3.继续跟踪946黑卡死问题，执行指定的行动项。"
