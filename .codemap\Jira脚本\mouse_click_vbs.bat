@echo off
title Mouse Auto Click Tool

echo ========================================
echo         Mouse Auto Click Tool
echo ========================================
echo.
echo Instructions:
echo 1. Move mouse to target position
echo 2. Press Enter to start clicking
echo 3. Press Ctrl+C to stop
echo ========================================
echo.

set /p interval="Enter click interval in seconds (default 1): "
if "%interval%"=="" set interval=1

echo.
echo Move mouse to target position and press Enter...
pause >nul

echo.
echo Creating VBScript helper...

rem Create VBScript file for mouse clicking
echo Set objShell = CreateObject("WScript.Shell") > click.vbs
echo Set objExcel = CreateObject("Excel.Application") >> click.vbs
echo objExcel.Visible = False >> click.vbs
echo Set objWorkbook = objExcel.Workbooks.Add >> click.vbs
echo Set objWorksheet = objWorkbook.Worksheets(1) >> click.vbs
echo objWorksheet.Cells(1,1).Value = "Click" >> click.vbs
echo objWorksheet.Cells(1,1).Select >> click.vbs
echo objExcel.SendKeys "{ENTER}" >> click.vbs
echo objWorkbook.Close False >> click.vbs
echo objExcel.Quit >> click.vbs

echo Starting auto click every %interval% seconds...
echo Press Ctrl+C to stop
echo.

set count=0

:loop
set /a count+=1
echo Click %count% at %time%

rem Use PowerShell to simulate mouse click at current position
powershell.exe -Command ^
"Add-Type -AssemblyName System.Windows.Forms; ^
Add-Type -AssemblyName System.Drawing; ^
$signature = '[DllImport(\"user32.dll\",CharSet=CharSet.Auto,CallingConvention=CallingConvention.StdCall)] public static extern void mouse_event(long dwFlags, long dx, long dy, long cButtons, long dwExtraInfo);'; ^
$SendMouseClick = Add-Type -memberDefinition $signature -name \"Win32MouseEventNew\" -namespace Win32Functions -passThru; ^
$SendMouseClick::mouse_event(0x00000002, 0, 0, 0, 0); ^
Start-Sleep -Milliseconds 100; ^
$SendMouseClick::mouse_event(0x00000004, 0, 0, 0, 0);"

timeout /t %interval% /nobreak >nul
goto loop
