# Datasource 插件问题分析文档

### 1. 单例模式实现不完善

**问题**：Publisher 类使用了单例模式，但实现不完善。

**代码**：
```cpp
// Publisher.h
class Publisher {
public:
    static Publisher* GetInstance();
    ~Publisher();
    // ...
private:
    static Publisher* sPubInst;
    // ...
};

// Publisher.cpp
Publisher* Publisher::sPubInst = nullptr;

Publisher* Publisher::GetInstance() {
    if (nullptr == sPubInst) {
        sPubInst = new Publisher();
        sPubInst->mThreadRunning = 1;
    }
    return sPubInst;
}

Publisher::~Publisher() {
    DelAllSubs();
}
```

**风险**：
- 没有考虑线程安全问题，多线程环境下可能创建多个实例
- 没有实现完整的单例生命周期管理，可能导致内存泄漏
- 析构函数中调用 DelAllSubs()，但没有保证在程序结束时一定会调用析构函数
- 没有防止外部复制或移动构造的机制

---

### 2. 数据访问没有同步保护

**问题**：多个 map 数据结构的访问没有同步保护。

**代码**：
```cpp
// Publisher.h
class Publisher {
private:
    std::map<FString, ASubscriber*> mSubMap;
    std::unordered_map<std::string, int> mStoreMapInt;
    std::unordered_map<std::string, float> mStoreMapFloat;
    std::unordered_map<std::string, std::string> mStoreMapStr;
    std::unordered_map<std::string, TArray<float>> mStoreMapTarryFloat;
    std::unordered_map<std::string, TArray<FVector>> mStoreMapTarryVector;
    std::unordered_map<std::string, TMap<int,FVector>> mStoreMapTMapVector;
    // ...
};

// Publisher.cpp
void Publisher::PubMsg(std::string topic, int value) {
    // ...
    auto findValueIt = mStoreMapInt.find(topic);
    bool update = true;
    // ...
    mStoreMapInt[topic] = value;
    // ...
}
```

**风险**：
- 多线程环境下可能发生数据竞争
- 可能导致数据不一致或崩溃
- 难以调试的随机问题
- 在高并发场景下可能出现未定义行为

---

### 3. 缺乏资源释放机制

**问题**：部分资源没有明确的释放机制，特别是在异常情况下。

**代码**：
```cpp
// Datasource.cpp
UDatasourceImpl* UDatasource::CreateAndStartDatasource() {
    if (!sDatasourceImpl) {
        sDatasourceImpl = NewObject<UDatasourceImpl>();
        sDatasourceImpl->AddToRoot();
        sDatasourceImpl->InitPublisher();
        // ...
    }
    return sDatasourceImpl;
}

void UDatasource::StopDatasource() {
    if (sDatasourceImpl) {
        sDatasourceImpl->RemoveAllSubs();
        sDatasourceImpl = nullptr;
    }
}
```

**风险**：
- 长时间运行可能导致内存泄漏
- 资源未及时释放可能影响性能
- 在异常情况下可能无法正确清理资源
- 没有处理异常情况下的资源清理

---

### 4. UObject 管理不规范

**问题**：UDatasourceImpl 使用 AddToRoot() 防止垃圾回收，但没有在适当时机调用 RemoveFromRoot()。

**代码**：
```cpp
// Datasource.cpp
UDatasourceImpl* UDatasource::CreateAndStartDatasource() {
    if (!sDatasourceImpl) {
        sDatasourceImpl = NewObject<UDatasourceImpl>();
        sDatasourceImpl->AddToRoot();
        // ...
    }
    return sDatasourceImpl;
}

void UDatasource::StopDatasource() {
    if (sDatasourceImpl) {
        sDatasourceImpl->RemoveAllSubs();
        sDatasourceImpl = nullptr;
        // 没有调用 RemoveFromRoot()
    }
}
```

**风险**：
- 可能导致 UObject 泄漏
- 影响 UE 垃圾回收效率
- 可能导致对象在不需要时仍然存活
- 内存占用持续增长

---

### 5. 注释和文档不足

**问题**：代码中缺乏足够的注释和文档，特别是关键算法和复杂逻辑。

**代码**：
```cpp
// Publisher.cpp
void Publisher::PubMsg(std::string topic, int value) {
    /************************此处为测试代码*************************************************/
    if (mThreadRunning == 0) {
        return;
    }
    // 查找并判断是否需要更新
    auto findValueIt = mStoreMapInt.find(topic);
    //bool update = (findValueIt != mStoreMapInt.end()&&findValueIt->second == value);
    bool update = true;
    if (update) {
        // ...
    }
}

// Subscriber.h
// 没有对枚举类型的详细说明
UENUM(BlueprintType)
enum class EVehicleEventType : uint8
{
    System_Change,
    GearType,
    DayNightMode,
    // ...
};
```

**风险**：
- 难以理解代码
- 维护困难，容易引入新问题
- 功能使用不当可能导致问题
- 代码意图不明确，难以理解设计决策

---

### 6. 代码重复

**问题**：存在大量重复代码，特别是在数据类型处理方面。

**代码**：
```cpp
// DatasourceImpl.cpp
void UDatasourceImpl::NotifyPlatformInt(FString& topicName, int& value) {
    std::string name = TCHAR_TO_UTF8(*topicName);
#if WITH_EDITOR
    hege::Publisher::GetInstance()->PubMsg(name, value);
#endif
    CallbackPlatformIntValue(name, value);
}

void UDatasourceImpl::NotifyPlatformFloat(FString& topicName, float& value) {
    std::string name = TCHAR_TO_UTF8(*topicName);
#if WITH_EDITOR
    hege::Publisher::GetInstance()->PubMsg(name, value);
#endif
    CallbackPlatformFloatValue(name, value);
}

void UDatasourceImpl::NotifyPlatformString(FString& topicName, FString& value) {
    std::string name = TCHAR_TO_UTF8(*topicName);
    std::string vvalue = TCHAR_TO_UTF8(*value);
#if WITH_EDITOR
    hege::Publisher::GetInstance()->PubMsg(name, vvalue);
#endif
    CallbackPlatformStringValue(name, vvalue);
}

// 类似的重复代码还有很多...
```

**风险**：
- 修改一处需要同步修改多处
- 容易引入不一致性
- 增加维护难度
- 代码数量庞大，可读性很低

---

### 7. 错误检查不一致

**问题**：错误检查不一致，有些地方检查空指针，有些地方没有。

**代码**：
```cpp
// AndroidDataFeeder.cpp
JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetStringValue(JNIEnv* env, jclass clazz, jstring name, jstring value) {
    if (nullptr == env || nullptr == sDatasource) {
        ALOGD("!!! NotifyStringValue Error!!! env or actor is NULL");
        return;
    }
    // ...
}

// DatasourceImpl.cpp
void UDatasourceImpl::NotifyPlatformInt(FString& topicName, int& value) {
    std::string name = TCHAR_TO_UTF8(*topicName);
    // 没有检查 topicName 是否为空
#if WITH_EDITOR
    hege::Publisher::GetInstance()->PubMsg(name, value);
    // 没有检查 Publisher::GetInstance() 是否为空
#endif
    CallbackPlatformIntValue(name, value);
}
```

**风险**：
- 可能导致空指针访问崩溃
- 错误处理不完整
- 程序行为不可预测
- 难以追踪和修复错误

---

### 8. 缺乏配置机制

**问题**：插件缺乏灵活的配置机制，大多数参数都是硬编码的。

**代码**：
```cpp
// Publisher.cpp
Publisher* Publisher::GetInstance() {
    if (nullptr == sPubInst) {
        sPubInst = new Publisher();
        sPubInst->mThreadRunning = 1; // 硬编码的初始值
    }
    return sPubInst;
}

// AndroidDataFeeder.h
#define LOG_TAG "C++" // 硬编码的日志标签

//定义日志打印宏函数
#define ALOGI(...)  __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define ALOGE(...)  __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
```

**风险**：
- 难以适应不同项目需求
- 修改配置需要重新编译
- 不同环境下难以调整行为

---

### 9. 缺乏数据验证

**问题**：接收到的数据没有进行有效性验证。

**代码**：
```cpp
// Publisher.cpp
void Publisher::PubMsg(std::string topic, int value) {
    // 没有验证 topic 是否有效
    auto findValueIt = mStoreMapInt.find(topic);
    bool update = true;
    mStoreMapInt[topic] = value; // 直接存储，没有验证
    // ...
}

// AndroidDataFeeder.cpp
JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetIntValue(JNIEnv* env, jclass clazz, jstring name, jint value) {
    // ...
    const char* nameStr = env->GetStringUTFChars(name, NULL);
    FString nameString(UTF8_TO_TCHAR(nameStr));
    int intValue = value;
    // 没有验证 value 的范围或有效性
    sDatasource->SetIntValue(nameString, intValue);
    // ...
}
```

**风险**：
- 无效数据可能导致程序崩溃
- 难以判断数据来源的可靠性

