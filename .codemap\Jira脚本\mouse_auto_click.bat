@echo off
echo ========================================
echo         Mouse Auto Click Script
echo ========================================
echo.
echo Instructions:
echo 1. Move mouse to target position
echo 2. Press any key to start clicking
echo 3. Press Ctrl+C to stop
echo.
echo Warning: Make sure target position is correct
echo ========================================
echo.

set /p interval="Enter click interval in seconds (default 1): "
if "%interval%"=="" set interval=1

echo.
echo Move mouse to target position...
pause

echo.
echo Starting auto click, interval %interval% seconds...
echo Press Ctrl+C to stop
echo.

powershell.exe -ExecutionPolicy Bypass -Command ^
"Add-Type -AssemblyName System.Windows.Forms; ^
Add-Type -AssemblyName System.Drawing; ^
Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Mouse { [DllImport(\"user32.dll\")] public static extern void mouse_event(int dwFlags, int dx, int dy, int cButtons, int dwExtraInfo); public const int MOUSEEVENTF_LEFTDOWN = 0x02; public const int MOUSEEVENTF_LEFTUP = 0x04; }'; ^
$pos = [System.Windows.Forms.Cursor]::Position; ^
Write-Host ('Mouse position: X=' + $pos.X + ', Y=' + $pos.Y); ^
Write-Host 'Starting auto click...'; ^
$count = 0; ^
while ($true) { ^
    try { ^
        [System.Windows.Forms.Cursor]::Position = New-Object System.Drawing.Point($pos.X, $pos.Y); ^
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0); ^
        Start-Sleep -Milliseconds 50; ^
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTUP, 0, 0, 0, 0); ^
        $count++; ^
        Write-Host ('Click count: ' + $count + ' - ' + (Get-Date).ToString('HH:mm:ss')); ^
        Start-Sleep -Seconds %interval%; ^
    } catch { ^
        Write-Host ('Error: ' + $_.Exception.Message); ^
        break; ^
    } ^
}"

echo.
echo Script stopped
pause
