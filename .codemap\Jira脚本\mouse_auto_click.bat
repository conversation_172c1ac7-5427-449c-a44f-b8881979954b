@echo off
chcp 65001 >nul
echo ========================================
echo         鼠标自动点击脚本
echo ========================================
echo.
echo 使用说明：
echo 1. 运行脚本后，将鼠标移动到要点击的位置
echo 2. 按任意键开始自动点击
echo 3. 按 Ctrl+C 停止脚本
echo.
echo 注意：请确保目标位置正确，避免误操作
echo ========================================
echo.

set /p interval="请输入点击间隔（秒，默认为1）: "
if "%interval%"=="" set interval=1

echo.
echo 请将鼠标移动到要点击的位置...
pause

echo.
echo 开始自动点击，间隔 %interval% 秒...
echo 按 Ctrl+C 停止脚本
echo.

powershell -Command "
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 获取当前鼠标位置
$pos = [System.Windows.Forms.Cursor]::Position
Write-Host '鼠标位置: X=' $pos.X ', Y=' $pos.Y
Write-Host '开始自动点击...'
Write-Host ''

$count = 0
while ($true) {
    try {
        # 模拟鼠标左键点击
        [System.Windows.Forms.Cursor]::Position = New-Object System.Drawing.Point($pos.X, $pos.Y)
        
        # 使用Windows API进行鼠标点击
        Add-Type -TypeDefinition '
            using System;
            using System.Runtime.InteropServices;
            public class Mouse {
                [DllImport(\"user32.dll\")]
                public static extern void mouse_event(int dwFlags, int dx, int dy, int cButtons, int dwExtraInfo);
                public const int MOUSEEVENTF_LEFTDOWN = 0x02;
                public const int MOUSEEVENTF_LEFTUP = 0x04;
            }
        '
        
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        Start-Sleep -Milliseconds 50
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        
        $count++
        Write-Host ('点击次数: ' + $count + ' - ' + (Get-Date).ToString('HH:mm:ss'))
        
        Start-Sleep -Seconds %interval%
    }
    catch {
        Write-Host '发生错误: ' $_.Exception.Message
        break
    }
}
"

echo.
echo 脚本已停止
pause
