# 虚幻引擎源码编译提速

### 1.增加线程数量

打开Setup.bat，开启20个线程，这个可以根据CPU的核心数量来定：

```cmd
set PROMPT_ARGUMENT= --prompt --threads=20
```

### 2.剔除不需要的模块

可以根据个人需要，可以让UE不要编译和准备一些用不到的模块，比如：

```cmd
set PROMPT_ARGUMENT= --prompt --threads=20 --exclude=VS2012 --exclude=VS2013
```

这样就不会为VS2012和VS2013准备相关的编译资源。

如果不需要H5打包、Ios打包和Linux打包，也可以这样改：

```cmd
set PROMPT_ARGUMENT= --prompt --threads=20 --exclude=VS2012 --exclude=VS2013 --exclude=HTML5 --exclude=Linux --exclude=iOS
```

### 3.添加CDN

修改C:\Windows\System32\drivers\etc\hosts文件，添加如下一行：

************ cdn.unrealengine.com

这样可以显著加快网络连接的速度，不需要靠域名服务器解析域名。





