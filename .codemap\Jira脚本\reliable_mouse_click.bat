@echo off
title Mouse Auto Click Tool

echo ========================================
echo         Mouse Auto Click Tool
echo ========================================
echo.
echo Instructions:
echo 1. Move mouse to target position
echo 2. Press Enter to start clicking
echo 3. Press Ctrl+C to stop
echo ========================================
echo.

set /p interval="Enter click interval in seconds (default 1): "
if "%interval%"=="" set interval=1

echo.
echo Move mouse to target position and press Enter...
pause >nul

echo.
echo Starting auto click every %interval% seconds...
echo Press Ctrl+C to stop
echo.

rem Create PowerShell script file
echo Add-Type -AssemblyName System.Windows.Forms > mouseclick.ps1
echo Add-Type -AssemblyName System.Drawing >> mouseclick.ps1
echo. >> mouseclick.ps1
echo $signature = @' >> mouseclick.ps1
echo [DllImport("user32.dll",CharSet=CharSet.Auto, CallingConvention=CallingConvention.StdCall)] >> mouseclick.ps1
echo public static extern void mouse_event(long dwFlags, long dx, long dy, long cButtons, long dwExtraInfo); >> mouseclick.ps1
echo '@ >> mouseclick.ps1
echo. >> mouseclick.ps1
echo $SendMouseClick = Add-Type -memberDefinition $signature -name "Win32MouseEventNew" -namespace Win32Functions -passThru >> mouseclick.ps1
echo. >> mouseclick.ps1
echo # Left mouse button down >> mouseclick.ps1
echo $SendMouseClick::mouse_event(0x00000002, 0, 0, 0, 0) >> mouseclick.ps1
echo Start-Sleep -Milliseconds 100 >> mouseclick.ps1
echo # Left mouse button up >> mouseclick.ps1
echo $SendMouseClick::mouse_event(0x00000004, 0, 0, 0, 0) >> mouseclick.ps1

set count=0

:loop
set /a count+=1
echo Click %count% at %time%

powershell.exe -ExecutionPolicy Bypass -File mouseclick.ps1

timeout /t %interval% /nobreak >nul
goto loop
