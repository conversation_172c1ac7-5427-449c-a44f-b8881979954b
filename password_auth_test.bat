@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    密码认证测试 (仅用于诊断)
echo ========================================
echo.
echo 警告：此脚本仅用于测试API是否支持密码认证
echo 请不要在生产环境中使用密码认证
echo.

set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

echo 如果您的Jira不支持API Token，可能需要使用密码认证
echo.
set /p PASSWORD="请输入您的Jira密码（仅用于测试）: "

if "!PASSWORD!"=="" (
    echo 未输入密码，退出测试
    pause
    exit /b 1
)

echo.
echo 测试密码认证...
curl -v -u "!USERNAME!:!PASSWORD!" "!JIRA_URL!/rest/api/2/myself" 2>&1 | findstr /i "HTTP\|401\|403\|200\|302"

echo.
echo 测试完成！
echo.
echo 如果看到 HTTP/1.1 200 OK，说明需要使用密码而不是API Token
echo 如果仍然失败，说明有其他问题

pause
