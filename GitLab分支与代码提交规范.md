# GitLab分支与代码提交规范

## 目录

- [1. 分支管理策略](#1-分支管理策略)
- [2. 分支命名规范](#2-分支命名规范)
- [3. 提交消息规范](#3-提交消息规范)
- [4. 代码审查流程](#4-代码审查流程)
- [5. 合并请求规范](#5-合并请求规范)
- [6. 版本标签管理](#6-版本标签管理)
- [7. 常见问题与解决方案](#7-常见问题与解决方案)
- [8. 附录：Git常用命令](#8-附录git常用命令)

## 1. 分支管理策略

我们采用基于Git Flow的分支管理策略，主要包含以下类型的分支：

### 1.1 长期分支

- **master/main**: 主分支，存放稳定的生产环境代码
- **develop**: 开发分支，存放最新的开发代码

### 1.2 临时分支

- **feature**: 功能分支，用于开发新功能
- **bugfix**: 缺陷修复分支，用于修复非生产环境的缺陷
- **hotfix**: 热修复分支，用于修复生产环境的紧急缺陷
- **release**: 发布分支，用于版本发布前的准备工作
- **docs**: 文档分支，用于文档更新
- **refactor**: 重构分支，用于代码重构

### 1.3 分支工作流

1. 从 `develop` 分支创建功能分支进行开发
2. 开发完成后，提交合并请求到 `develop` 分支
3. 版本发布前，从 `develop` 分支创建 `release` 分支
4. 测试通过后，将 `release` 分支合并到 `master` 和 `develop` 分支
5. 生产环境出现紧急问题时，从 `master` 分支创建 `hotfix` 分支
6. 修复完成后，将 `hotfix` 分支合并到 `master` 和 `develop` 分支

## 2. 分支命名规范

### 2.1 分支前缀

- 功能分支：`feature/`
- 缺陷修复分支：`bugfix/`
- 热修复分支：`hotfix/`
- 发布分支：`release/`
- 文档分支：`docs/`
- 重构分支：`refactor/`

### 2.2 分支命名格式

```
<前缀>/<任务编号>-<简短描述>
```

### 2.3 命名示例

- `feature/123-user-login`
- `bugfix/456-fix-memory-leak`
- `hotfix/789-critical-security-issue`
- `release/v1.2.0`
- `docs/update-api-docs`
- `refactor/optimize-database-queries`

### 2.4 注意事项

- 分支名称使用小写字母
- 单词之间使用连字符 `-` 分隔
- 简短描述应简明扼要，不超过50个字符
- 尽量包含任务编号（如Jira/GitLab Issue ID）

## 3. 提交消息规范

### 3.1 提交消息格式

```
<类型>(<范围>): <主题>

<正文>

<页脚>
```

### 3.2 类型（Type）

| 类型 | 描述 | 示例场景 |
|------|------|----------|
| `feat` | 新功能 | 添加登录功能、实现支付模块 |
| `fix` | Bug修复 | 修复登录失败、解决内存泄漏 |
| `docs` | 文档更新 | 更新README、添加API文档 |
| `style` | 代码格式 | 修复缩进、移除空行 |
| `refactor` | 重构 | 优化算法、重构组件 |
| `perf` | 性能优化 | 提升查询效率、优化渲染 |
| `test` | 测试 | 添加单元测试、修复测试用例 |
| `build` | 构建系统 | 更新构建配置、修改构建脚本 |
| `ci` | CI/CD | 更新CI配置、修复部署流程 |
| `chore` | 其他 | 更新依赖、删除无用文件 |
| `revert` | 回滚 | 撤销之前的提交 |

### 3.3 范围（Scope）

描述本次提交影响的模块或组件，可选但推荐：

- `auth`：认证模块
- `api`：API接口
- `ui`：用户界面
- `db`：数据库
- `config`：配置文件
- `deps`：依赖管理

### 3.4 主题（Subject）

- 使用祈使句，现在时
- 首字母小写
- 不超过50个字符
- 不以句号结尾

### 3.5 正文（Body）

- 详细描述为什么做这个改动
- 说明实现方式
- 每行不超过72个字符
- 与主题之间空一行

### 3.6 页脚（Footer）

- 引用相关的Issue或PR
- 标记不兼容变更
- 格式：`Closes #123` 或 `BREAKING CHANGE: 描述不兼容变更`

### 3.7 提交消息示例

```
feat(auth): 添加用户登录验证功能

- 实现基于JWT的认证机制
- 添加密码强度检查
- 增加登录失败次数限制

Closes #123
```

## 4. 代码审查流程

### 4.1 审查原则

- 每个合并请求必须经过至少一名团队成员的审查
- 代码审查应关注代码质量、功能实现和安全性
- 审查应在提交合并请求后的24小时内完成

### 4.2 审查重点

- 代码是否符合项目编码规范
- 功能实现是否符合需求
- 是否有潜在的安全问题
- 是否有性能优化空间
- 测试覆盖率是否足够

### 4.3 审查流程

1. 开发者提交合并请求
2. 指定审查者进行代码审查
3. 审查者提出修改建议
4. 开发者根据建议进行修改
5. 审查者确认修改并批准合并
6. 项目负责人执行最终合并

## 5. 合并请求规范

### 5.1 合并请求标题

格式：`[<类型>] <简短描述> (#<任务编号>)`

示例：`[Feature] 实现用户登录功能 (#123)`

### 5.2 合并请求描述模板

```markdown
## 功能描述
简要描述本次合并的功能或修复的问题

## 实现方案
描述实现方案和技术选择

## 测试情况
描述测试方法和测试结果

## 相关链接
- 关联的需求文档
- 关联的设计文档
- 关联的Issue

## 截图（如有）
添加相关截图
```

### 5.3 合并策略

- 功能分支合并到开发分支：使用 `Squash and merge`
- 发布分支合并到主分支：使用 `Merge commit`
- 热修复分支合并：使用 `Cherry-pick`

## 6. 版本标签管理

### 6.1 版本号规范

采用语义化版本（Semantic Versioning）规范：`主版本号.次版本号.修订号`

- 主版本号：不兼容的API变更
- 次版本号：向下兼容的功能新增
- 修订号：向下兼容的问题修复

### 6.2 标签命名规范

格式：`v<版本号>`

示例：`v1.2.3`

### 6.3 版本发布流程

1. 在 `release` 分支上完成版本准备工作
2. 更新版本号和更新日志
3. 合并到 `master` 分支
4. 在 `master` 分支上创建标签
5. 推送标签到远程仓库

## 7. 常见问题与解决方案

### 7.1 合并冲突

**问题**：合并分支时出现冲突

**解决方案**：
1. 先拉取最新的目标分支代码
2. 在本地解决冲突
3. 提交解决冲突的更改
4. 重新推送到远程仓库

### 7.2 误提交敏感信息

**问题**：不小心提交了敏感信息（如密码、密钥）

**解决方案**：
1. 使用 `git filter-branch` 或 BFG Repo-Cleaner 工具从历史记录中删除敏感信息
2. 更新 `.gitignore` 文件，避免再次提交
3. 强制推送到远程仓库（需谨慎操作）

### 7.3 大文件处理

**问题**：需要管理大文件

**解决方案**：
1. 使用 Git LFS（Large File Storage）管理大文件
2. 配置 `.gitattributes` 文件，指定需要用LFS管理的文件类型
3. 使用 `git lfs track` 命令跟踪大文件

## 8. 附录：Git常用命令

### 8.1 分支操作

```bash
# 查看所有分支
git branch -a

# 创建新分支
git checkout -b feature/new-feature

# 切换分支
git checkout develop

# 删除本地分支
git branch -d feature/old-feature

# 删除远程分支
git push origin --delete feature/old-feature
```

### 8.2 提交操作

```bash
# 查看状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "feat: 添加新功能"

# 修改最后一次提交
git commit --amend

# 推送到远程
git push origin feature/new-feature
```

### 8.3 合并操作

```bash
# 合并分支
git merge feature/new-feature

# 变基操作
git rebase develop

# 解决冲突后继续变基
git rebase --continue

# 取消变基
git rebase --abort
```

### 8.4 标签操作

```bash
# 创建标签
git tag v1.0.0

# 创建带注释的标签
git tag -a v1.0.0 -m "版本1.0.0发布"

# 推送标签到远程
git push origin v1.0.0

# 推送所有标签
git push origin --tags
```
