# GitLab代码提交流程图

## 完整代码提交流程

```mermaid
flowchart TD
    Start([开始]) --> CheckBranch{检查当前分支}
    CheckBranch -->|在主分支| CreateBranch[创建新分支]
    CheckBranch -->|在开发分支| CreateBranch
    CheckBranch -->|已在功能分支| DevWork
    
    CreateBranch --> NamingBranch["按规范命名分支<br/>feature/123-user-login<br/>bugfix/456-fix-memory-leak<br/>hotfix/789-critical-security-issue<br/>等"]
    NamingBranch --> DevWork[进行开发工作]
    
    DevWork --> CheckLargeFiles{是否有大文件?}
    CheckLargeFiles -->|是| UseLFS["使用Git LFS<br/>git lfs track '*.psd'<br/>git add .gitattributes"]
    CheckLargeFiles -->|否| CommitChanges
    UseLFS --> CommitChanges
    
    CommitChanges --> CommitMsg["按规范提交代码<br/><类型>(<范围>): <主题><br/>feat(login): 实现用户登录功能"]
    CommitMsg --> PushChanges["推送到远程分支<br/>git push origin feature/123-user-login"]
    
    PushChanges --> CreateMR["创建合并请求(MR)<br/>遵循MR描述模板"]
    CreateMR --> CodeReview["代码审查<br/>至少一名团队成员审查"]
    
    CodeReview --> ReviewResult{审查结果}
    ReviewResult -->|需要修改| FixIssues[修复问题]
    FixIssues --> CommitChanges
    
    ReviewResult -->|通过| MergeStrategy{选择合并策略}
    MergeStrategy -->|功能分支到开发分支| SquashMerge["Squash and merge"]
    MergeStrategy -->|发布分支到主分支| MergeCommit["Merge commit"]
    MergeStrategy -->|热修复分支| CherryPick["Cherry-pick"]
    
    SquashMerge --> MergeDone[合并完成]
    MergeCommit --> MergeDone
    CherryPick --> MergeDone
    
    MergeDone --> CleanUp["清理工作<br/>删除已合并的功能分支"]
    CleanUp --> End([结束])
```

## 分支管理策略流程

```mermaid
flowchart TD
    Start([开始]) --> BranchType{分支类型}
    
    BranchType -->|新功能开发| Feature["从develop分支创建<br/>feature/123-user-login"]
    BranchType -->|Bug修复| Bugfix["从develop分支创建<br/>bugfix/456-fix-memory-leak"]
    BranchType -->|紧急修复| Hotfix["从master分支创建<br/>hotfix/789-critical-security-issue"]
    BranchType -->|版本发布| Release["从develop分支创建<br/>release/v1.2.0"]
    
    Feature --> DevWork[进行开发工作]
    Bugfix --> DevWork
    Hotfix --> FixWork[进行修复工作]
    Release --> PrepareRelease[准备发布工作]
    
    DevWork --> TestLocal[本地测试]
    FixWork --> TestLocal
    PrepareRelease --> TestRelease[版本测试]
    
    TestLocal --> MRToDevelop["创建MR到develop分支"]
    TestRelease --> MRToMaster["创建MR到master分支"]
    TestRelease --> MRToDevelop
    
    MRToDevelop --> ReviewProcess[代码审查流程]
    MRToMaster --> ReviewProcess
    
    ReviewProcess --> MergeToDevelop["合并到develop分支"]
    ReviewProcess --> MergeToMaster["合并到master分支"]
    
    MergeToMaster --> CreateTag["创建版本标签<br/>v1.2.0"]
    
    MergeToDevelop --> End([结束])
    MergeToMaster --> End
    CreateTag --> End
```

## Git LFS大文件处理流程

```mermaid
flowchart TD
    Start([开始]) --> CheckFile{检查文件类型}
    
    CheckFile -->|大型二进制文件| UseLFS[使用Git LFS]
    CheckFile -->|普通代码文件| NormalGit[使用普通Git流程]
    
    UseLFS --> InstallLFS["安装Git LFS<br/>git lfs install"]
    InstallLFS --> TrackFiles["跟踪大文件<br/>git lfs track '*.psd'<br/>git lfs track '*.zip'"]
    
    TrackFiles --> CommitAttributes["提交.gitattributes<br/>git add .gitattributes<br/>git commit -m 'chore: 配置Git LFS'"]
    
    CommitAttributes --> AddLargeFiles["添加大文件<br/>git add large-file.psd"]
    AddLargeFiles --> CommitLargeFiles["提交大文件<br/>git commit -m 'feat: 添加设计文件'"]
    
    CommitLargeFiles --> PushLFS["推送到远程<br/>git push origin feature/branch"]
    
    PushLFS --> End([结束])
    NormalGit --> End
```

## 代码审查流程

```mermaid
flowchart TD
    Start([开始]) --> SubmitMR["开发者提交合并请求<br/>[Feature] 实现用户登录功能 (#123)"]
    
    SubmitMR --> AssignReviewer["指定代码审查者"]
    AssignReviewer --> ReviewCode["审查者进行代码审查<br/>- 代码规范<br/>- 功能实现<br/>- 安全问题<br/>- 性能优化<br/>- 测试覆盖"]
    
    ReviewCode --> ReviewResult{审查结果}
    ReviewResult -->|需要修改| ProvideComments["审查者提出修改建议"]
    ProvideComments --> FixIssues["开发者根据建议修改"]
    FixIssues --> PushChanges["推送修改"]
    PushChanges --> ReviewCode
    
    ReviewResult -->|通过| ApproveChanges["审查者批准合并"]
    ApproveChanges --> ProjectOwner["项目负责人执行最终合并"]
    
    ProjectOwner --> MergeStrategy{选择合并策略}
    MergeStrategy -->|功能分支| SquashMerge["Squash and merge"]
    MergeStrategy -->|发布分支| MergeCommit["Merge commit"]
    MergeStrategy -->|热修复分支| CherryPick["Cherry-pick"]
    
    SquashMerge --> MergeDone[合并完成]
    MergeCommit --> MergeDone
    CherryPick --> MergeDone
    
    MergeDone --> End([结束])
```

## Fork工作流程

```mermaid
flowchart TD
    Start([开始]) --> ForkRepo["从主仓库Fork出个人仓库"]
    
    ForkRepo --> CloneRepo["克隆个人Fork的仓库<br/>git clone https://gitlab.com/your-username/project-name.git"]
    CloneRepo --> AddUpstream["添加上游仓库<br/>git remote add upstream https://gitlab.com/organization/project-name.git"]
    
    AddUpstream --> SyncUpstream["同步上游仓库<br/>git fetch upstream<br/>git merge upstream/main"]
    SyncUpstream --> CreateBranch["创建功能分支<br/>git checkout -b feature/new-feature"]
    
    CreateBranch --> DevWork[进行开发工作]
    DevWork --> CommitChanges["提交更改<br/>git add .<br/>git commit -m 'feat: 添加新功能'"]
    
    CommitChanges --> PushChanges["推送到个人Fork仓库<br/>git push origin feature/new-feature"]
    PushChanges --> CreateMR["创建合并请求<br/>源：个人Fork仓库的功能分支<br/>目标：项目主仓库的develop分支"]
    
    CreateMR --> CodeReview[代码审查流程]
    CodeReview --> MergeResult{合并结果}
    
    MergeResult -->|已合并| DeleteBranch["删除已合并的功能分支"]
    MergeResult -->|未合并| FixIssues["修复问题"]
    FixIssues --> CommitChanges
    
    DeleteBranch --> End([结束])
```