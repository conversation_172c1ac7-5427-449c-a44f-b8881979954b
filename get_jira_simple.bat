@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Jira 问题数量获取工具 (简化版)
echo ========================================
echo.

:: 配置信息
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

:: 安全地获取密码
echo 请输入您的Jira密码:
set /p PASSWORD=

if "!PASSWORD!"=="" (
    echo [错误] 密码不能为空！
    pause
    exit /b 1
)

:: 使用简单的JQL查询
set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"

echo [信息] 使用简化查询: !JQL_QUERY!
echo.

:: 构建API请求URL
set "API_URL=!JIRA_URL!/rest/api/2/search"
set "TEMP_FILE=%TEMP%\jira_simple_%RANDOM%.json"

echo [信息] 正在查询Jira问题...

:: 发送API请求
echo [调试] 发送请求到: !API_URL!
echo [调试] 用户名: !USERNAME!
echo [调试] JQL查询: !JQL_QUERY!
echo.

curl -v -u "!USERNAME!:!PASSWORD!" ^
     -H "Accept: application/json" ^
     -G "!API_URL!" ^
     --data-urlencode "jql=!JQL_QUERY!" ^
     --data-urlencode "maxResults=0" ^
     -o "!TEMP_FILE!" ^
     2>&1

if !errorlevel! neq 0 (
    echo [错误] API请求失败
    pause
    exit /b 1
)

:: 检查响应文件大小
for %%F in ("!TEMP_FILE!") do set "FILE_SIZE=%%~zF"
echo [调试] 响应文件大小: !FILE_SIZE! 字节

:: 显示响应内容
echo.
echo [调试] 完整响应内容:
echo ========================================
type "!TEMP_FILE!"
echo ========================================
echo.

:: 检查是否是HTML响应
findstr /i "<html>\|<body>\|<title>" "!TEMP_FILE!" >nul
if !errorlevel! equ 0 (
    echo [分析] 响应是HTML格式，不是JSON！
    echo 这通常表示：
    echo 1. 被重定向到登录页面
    echo 2. 认证失败
    echo 3. API端点不正确
    echo 4. 服务器返回错误页面
    echo.
) else (
    echo [分析] 响应不是HTML格式
)

:: 检查是否包含错误信息
findstr /i "error\|exception\|unauthorized\|forbidden" "!TEMP_FILE!" >nul
if !errorlevel! equ 0 (
    echo [分析] 响应包含错误信息
    echo.
)

:: 尝试解析JSON
echo [信息] 尝试解析JSON...
for /f "delims=" %%i in ('powershell -Command "try { $json = Get-Content '!TEMP_FILE!' | ConvertFrom-Json; Write-Output $json.total } catch { Write-Output 'JSON_ERROR' }"') do (
    set TOTAL_ISSUES=%%i
)

:: 保存错误响应用于分析
if "!TOTAL_ISSUES!"=="JSON_ERROR" (
    set "ERROR_FILE=jira_error_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "ERROR_FILE=!ERROR_FILE: =0!"
    copy "!TEMP_FILE!" "!ERROR_FILE!" >nul
    echo [错误] JSON解析失败！
    echo 错误响应已保存到: !ERROR_FILE!
    echo.
    echo 请检查上面的响应内容来确定问题
) else (
    echo ========================================
    echo 分配给您的未解决问题数量: !TOTAL_ISSUES!
    echo ========================================
)

del "!TEMP_FILE!"

pause
