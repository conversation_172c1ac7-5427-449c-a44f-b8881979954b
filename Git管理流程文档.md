软件研发组

# **一、项目立项阶段**

## **1. Group/Project创建与权限分配**

- 项目管理员在Gitlab上创建顶层Group（如：Org-Project）。
- 在Group下分别创建各模块代码仓库（如：android-repo、ue-repo等）、共用代码仓库（common-repo）和顶层集成仓库（integration-repo）。
- 各模块负责人由管理员分配为对应仓库的Maintainer权限，普通研发人员仅分配为Reporter（只读）权限。
- 顶层集成仓库仅项目负责人具备Maintainer权限，其他人仅为Reporter。
- 权限分配流程如下：

```Plain
flowchart TD
    Admin[项目管理员] --> Group[顶层Group]
    Group --> AndroidRepo[android-repo]
    Group --> UERepo[ue-repo]
    Group --> CommonRepo[common-repo]
    Group --> IntegrationRepo[integration-repo]
    AndroidRepo --> AndroidLead[模块负责人A-Maintainer]
    AndroidRepo --> AndroidDev[普通研发人员-Reporter]
    UERepo --> UELead[模块负责人B-Maintainer]
    UERepo --> UEDev[普通研发人员-Reporter]
    CommonRepo --> CommonLead[共用代码负责人-Maintainer]
    CommonRepo --> CommonDev[普通研发人员-Reporter]
    IntegrationRepo --> ProjectLead[项目负责人-Maintainer]
    IntegrationRepo --> Others[其他成员-Reporter]
```

## **2. 基本要求**

- 研发人员应熟练掌握git及Gitlab的基本操作，理解fork、merge request、submodule等概念。
- 仓库管理人员需具备权限管理、分支保护、成员分级授权等相关知识和操作能力，能够合理配置和维护项目权限体系，保障代码安全与协作规范。
- 各模块仓库管理人员应具备CI/CD能力及编辑CI流程的能力，能够根据项目需求维护和优化CI/CD流程。
- 管理人员对CI流程的修改也必须通过Merge Request方式进行，确保流程变更的可追溯与审核。

# **二、项目执行阶段**

## **3. 仓库管理与代码提交**

\- **项目主仓库（Project** **Repo****）禁止直接提交分支**，所有开发分支、功能分支、bugfix分支等均不得直接推送到主仓库。

\- **每位开发人员需从项目主仓库fork出个人仓库（Personal Fork** **Repo****）**，所有开发、提交、推送操作均在个人仓库进行。

- 个人仓库与主仓库保持定期同步（建议每日同步主仓库的主分支，如master/develop）。

\- 开发完成后，**只能从个人仓库向主仓库发起****Merge Request****（****MR****）**，主仓库不接受直接push。

## **4.** **Merge Request****与Issue关联**

\- 个人仓库向主仓库发起MR时，**MR标题必须与对应的issue进行关联**，具体关联方式请参考《[Git分支与代码提交规范](https://jmvzejewbx.feishu.cn/docx/DoR0dp7WXo4mK6x5XApcNXkHnCd)》：

​    \- 标题格式示例：`Fix: #1234 修复xxx问题` 或 `Feature: #5678 新增xxx功能`

​    \- MR描述需详细说明本次变更内容、影响范围、测试情况等。

- MR流程：
  - 个人仓库开发完成后，向主仓库发起MR。
  - 选择对应的目标分支（如develop、release-*等）。
  - 填写规范的标题与描述，确保与issue关联。
  - 由项目维护者/审核人进行代码审核、合并。

​    \5. **如merge目标未变但当前****MR****存在问题不允许合并时，应保持该MR处于hold状态，待修复后继续更新同一MR，不应重新创建新的MR，避免同一目标存在多个MR，确保记录的唯一性和完整性。**

## **5.** **CI/CD****流程规范**

\- **项目代码仓库的Merge Request会自动触发CI流程**，CI通过后才允许合并。

\- 各项目代码仓库需维护自身的CI配置，**CI内容应至少包含单元测试、代码规范审核等**，确保代码质量。

\- **顶层仓库（Integration Repo）的CI仅负责整体集成CI**，如多仓库集成测试、系统级验证、自动化部署等。

- 项目代码仓库的CI与顶层仓库CI相互独立，互不干扰。

\- **CI流程本身的变更必须通过Merge Request提交，需经过同行评审，最终由项目整体负责人执行合入，确保流程变更的规范性和安全性。**

## **6. 顶层仓库与子仓库/共用代码集成（git submodule）**

\- **项目应设立一个顶层集成仓库（Integration Repo）**，用于整体项目的集成与CI/CD流程管理。

- 顶层仓库通过`git submodule`方式管理各个子仓库（如android、ue等）、共用代码仓库（common-repo），并可引用其他项目或基础库中的共用代码。

\- **顶层仓库更新submodule或依赖关系时，必须通过Merge Request方式进行，且merge时需与项目里程碑（milestone）关联。**

\- **引用和依赖关系由各模块负责人提交到顶层仓库merge，项目负责人审核。**

\- **CI/CD流程、自动化集成、统一发布等操作均在顶层仓库进行**，保证各子仓库和共用代码的独立性与集成的统一性。

### **6.1 集成与依赖关系流程举例**

- 例如：android、ue、common代码分别在独立的代码仓库（android-repo、ue-repo、common-repo）中开发和维护。
- 顶层仓库（integration-repo）通过`git submodule`分别引用android-repo、ue-repo、common-repo，并可引用其他项目或基础库中的共用代码。

\- 开发人员分别在各自仓库进行开发、提交、MR操作，**顶层仓库不直接开发业务代码**。

\- 当android、ue或common仓库有新版本或依赖关系需要集成时，顶层仓库更新对应submodule的commit或依赖，**由模块负责人通过MR方式提交，MR需关联项目里程碑，项目负责人审核，触发集成CI流程，实现自动化集成与发布。**

## **7. 详细开发与集成流程**

### **7.1 个人开发与提交流程**

```Plain
flowchart TD
    A1[开发者fork主仓库] --> A2[本地开发/提交]
    A2 --> A3[推送到个人fork仓库]
    A3 --> A4[向主仓库发起MR]
    A4 --> A5[CI自动触发]
    A5 -->|通过| A6[代码审核]
    A5 -->|失败| A7[修复后继续更新同一MR]
    A6 -->|审核通过| A8[合并到主仓库]
    A6 -->|审核不通过| A7
    A7 --> A5
    subgraph MR管理
        direction LR
        note1[如MR存在问题不允许合并，保持MR hold，修复后继续在原MR上提交，不新建MR]
    end
```

### **7.2 顶层集成与依赖管理CI流程**

```Plain
flowchart TD
    B1[顶层仓库管理员fork顶层仓库到个人仓库] --> B2[更新submodule或依赖]
    B2 --> B3[提交集成MR并关联里程碑]
    B3 --> B4[项目负责人审核]
    B4 -->|关注里程碑、CI、测试报告、发版评审| B5{是否存在严重bug}
    B5 -->|无| B6[允许合入，触发顶层CI]
    B5 -->|有| B7[不合入或发起特殊发版评审]
    B7 --> B8[特殊发版评审流程通过，记录到wiki]
    B8 --> B6
    B6 --> B9[集成发布]
    subgraph 依赖管理
        direction LR
        note2[所有依赖和引用关系变更均需MR，负责人审核，确保唯一性和可追溯性]
    end
```

- 说明：
  - 顶层仓库管理员fork顶层仓库到个人仓库，更新依赖关系后通过MR提交给项目负责人审核。
  - 审核人需关注里程碑完成情况、CI状态、测试报告、发版评审记录等，综合决策是否允许合入。
  - 若无问题则允许合入并触发顶层CI，若存在严重bug则不合入，或发起特殊发版评审流程。
  - 特殊发版流程通过后需保留评审记录到wiki，确保流程合规可追溯。

# **三、项目结项阶段**

## **8. Commit与分支命名规范**

- Commit信息、分支命名、MR标题等需严格遵循《[GitLab分支与代码提交规范](https://jmvzejewbx.feishu.cn/docx/DoR0dp7WXo4mK6x5XApcNXkHnCd)》与本流程文件要求。
- 禁止私自重构、提交无关代码。

## **9. 权限管理方案**

\- **模块负责人**：具备各自项目代码仓库的Maintainer权限，可管理分支、合并MR、配置CI等。

\- **普通研发人员**：仅具备各项目代码仓库的Reporter权限，只能查看代码、提交issue，不能push/merge。

\- **共用代码负责人**：具备common-repo的Maintainer权限，负责共用代码的维护和集成。

\- **顶层集成仓库**：仅项目负责人具备Maintainer权限，其他成员仅为Reporter。

\- **权限变更**：如需调整权限，需由项目管理员统一操作并记录。

## **10. 其他说明**

- 项目主仓库、顶层仓库、各子仓库、共用代码仓库的管理权限、保护分支等应由项目管理员统一配置。
- 如有特殊需求或流程变更，需经团队讨论后修订本流程文件。