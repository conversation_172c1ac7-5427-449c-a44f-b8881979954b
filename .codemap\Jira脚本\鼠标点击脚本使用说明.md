# 鼠标自动点击脚本使用说明

## 脚本文件

1. **mouse_auto_click.bat** - 基础版本
2. **mouse_auto_click_advanced.bat** - 高级版本

## 功能特性

### 基础版本 (mouse_auto_click.bat)
- 单点持续点击
- 可设置点击间隔
- 显示点击次数和时间
- 支持 Ctrl+C 停止

### 高级版本 (mouse_auto_click_advanced.bat)
- **单点持续点击模式**
  - 可设置点击间隔
  - 可设置最大点击次数
  - 实时显示点击状态

- **多点循环点击模式**
  - 支持设置多个点击位置
  - 按顺序循环点击各个位置
  - 可自定义点击间隔

- **指定坐标点击模式**
  - 直接输入X、Y坐标
  - 精确定位点击位置
  - 适合重复性任务

## 使用方法

### 基础版本使用步骤
1. 双击运行 `mouse_auto_click.bat`
2. 输入点击间隔（秒）
3. 将鼠标移动到目标位置
4. 按任意键开始自动点击
5. 按 Ctrl+C 停止脚本

### 高级版本使用步骤
1. 双击运行 `mouse_auto_click_advanced.bat`
2. 选择功能模式（1-4）
3. 根据提示设置参数
4. 按照指引操作
5. 按 Ctrl+C 停止脚本

## 注意事项

⚠️ **重要提醒**
- 使用前请确保目标位置正确
- 避免在重要应用程序上误操作
- 建议先在安全环境下测试
- 脚本运行时请勿移动鼠标（除非是多点模式设置阶段）

## 技术实现

- 使用 PowerShell 调用 Windows API
- 通过 user32.dll 的 mouse_event 函数实现鼠标点击
- 支持获取和设置鼠标位置
- 实时显示点击状态和计数

## 适用场景

- 游戏挂机点击
- 重复性操作自动化
- 软件测试
- 演示和培训

## 系统要求

- Windows 操作系统
- PowerShell 支持
- 管理员权限（某些情况下可能需要）

## 故障排除

如果脚本无法正常工作：
1. 确保以管理员身份运行
2. 检查 PowerShell 执行策略
3. 确认目标应用程序允许外部输入
4. 检查防病毒软件是否阻止脚本运行

## 免责声明

此脚本仅供学习和合法用途使用。使用者需要遵守相关法律法规和软件使用条款。作者不承担因误用脚本造成的任何损失或后果。
