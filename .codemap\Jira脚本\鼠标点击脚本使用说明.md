# 鼠标自动点击脚本使用说明

## 脚本文件

### 推荐使用（按优先级排序）
1. **run_mouse_click.bat** + **mouse_click.py** - Python版本（最稳定）
2. **basic_mouse_click.bat** - 简单VBS版本
3. **reliable_mouse_click.bat** - PowerShell版本
4. **mouse_auto_click.bat** - 修复版本

### 其他版本
- **simple_mouse_click.bat** - 简化版本
- **mouse_click_vbs.bat** - VBS混合版本
- **mouse_auto_click_advanced.bat** - 高级版本（可能有编码问题）

## 功能特性

### 基础版本 (mouse_auto_click.bat)
- 单点持续点击
- 可设置点击间隔
- 显示点击次数和时间
- 支持 Ctrl+C 停止

### 高级版本 (mouse_auto_click_advanced.bat)
- **单点持续点击模式**
  - 可设置点击间隔
  - 可设置最大点击次数
  - 实时显示点击状态

- **多点循环点击模式**
  - 支持设置多个点击位置
  - 按顺序循环点击各个位置
  - 可自定义点击间隔

- **指定坐标点击模式**
  - 直接输入X、Y坐标
  - 精确定位点击位置
  - 适合重复性任务

## 使用方法

### 推荐使用步骤（Python版本）

1. 双击运行 `run_mouse_click.bat`
2. 如果提示Python未安装，请先安装Python
3. 输入点击间隔（秒）
4. 将鼠标移动到目标位置
5. 按Enter开始自动点击
6. 按 Ctrl+C 停止脚本

### 备用方案（VBS版本）

1. 双击运行 `basic_mouse_click.bat`
2. 输入点击间隔（秒）
3. 将鼠标移动到目标位置
4. 按Enter开始自动点击
5. 按 Ctrl+C 停止脚本

### 如果遇到编码问题

如果看到乱码，请使用英文界面的脚本版本，避免中文显示问题。

## 注意事项

⚠️ **重要提醒**
- 使用前请确保目标位置正确
- 避免在重要应用程序上误操作
- 建议先在安全环境下测试
- 脚本运行时请勿移动鼠标（除非是多点模式设置阶段）

## 技术实现

- 使用 PowerShell 调用 Windows API
- 通过 user32.dll 的 mouse_event 函数实现鼠标点击
- 支持获取和设置鼠标位置
- 实时显示点击状态和计数

## 适用场景

- 游戏挂机点击
- 重复性操作自动化
- 软件测试
- 演示和培训

## 系统要求

- Windows 操作系统
- PowerShell 支持
- 管理员权限（某些情况下可能需要）

## 故障排除

### 常见问题及解决方案

**问题1：出现中文乱码**
- 解决方案：使用英文界面的脚本版本（如 `basic_mouse_click.bat`）

**问题2：提示"不是内部或外部命令"**
- 解决方案：
  1. 确保以管理员身份运行
  2. 使用 `basic_mouse_click.bat` 或 Python版本
  3. 检查系统是否支持PowerShell

**问题3：Python版本无法运行**
- 解决方案：
  1. 安装Python：https://www.python.org/
  2. 确保Python已添加到系统PATH
  3. 重启命令提示符

**问题4：点击无效果**
- 解决方案：
  1. 确保目标应用程序在前台
  2. 检查是否需要管理员权限
  3. 尝试不同的脚本版本

**问题5：脚本被杀毒软件阻止**
- 解决方案：
  1. 将脚本文件夹添加到杀毒软件白名单
  2. 临时关闭实时保护
  3. 使用Python版本（通常更安全）

## 免责声明

此脚本仅供学习和合法用途使用。使用者需要遵守相关法律法规和软件使用条款。作者不承担因误用脚本造成的任何损失或后果。
