# 从源码版本构建和打包 Unreal Engine 4 引擎文档

## 1. 环境准备

### 1.1 下载 Unreal Engine 4 源码

1. **创建 GitHub 账户**:
   - 如果你还没有 GitHub 账户，请访问 [GitHub 官网](https://github.com/) 注册一个账户。

2. **获取 Epic Games 的 GitHub 访问权限**:
   - 访问 [Epic Games GitHub 页面](https://www.unrealengine.com/en-US/ue4-on-github)。
   - 登录你的 Epic Games 账户，按照说明将你的 GitHub 账户与 Epic Games 账户关联。

3. **克隆 Unreal Engine 4 源码**:
   - 打开命令提示符（Windows）或终端（Mac）。
   - 选择一个目录用于存放源码，例如 `C:\UnrealEngine`。
   - 输入以下命令克隆源码：
     ```bash
     git clone -b release https://github.com/EpicGames/UnrealEngine.git
     ```
   - 这将下载最新的稳定版本的 Unreal Engine 4 源码。

### 1.2 安装必要的软件

1. **安装 Visual Studio**:
   - 访问 [Visual Studio 官网](https://visualstudio.microsoft.com/)。
   - 下载并安装 Visual Studio Community 版本。
   - 在安装过程中，选择 **Desktop development with C++** 工作负载，并确保勾选以下组件：
     - **MSVC v142 - VS 2019 C++ x64/x86 build tools**（或最新版本）。
     - **Windows 10 SDK**（建议选择最新版本）。
     - **C++ CMake tools for Windows**（可选，但推荐）。

2. **安装 Git**（如果尚未安装）:
   - 访问 [Git 官网](https://git-scm.com/downloads)。
   - 下载并安装适合你操作系统的 Git 版本。

## 2. 构建 Unreal Engine 4

### 2.1 运行 Setup.bat

1. **导航到 Unreal Engine 源码目录**:
   - 在命令提示符中，输入以下命令：
     ```bash
     cd C:\UnrealEngine
     ```

2. **运行 Setup.bat**:
   - 输入以下命令以下载所需的依赖项：
     ```bash
     Setup.bat
     ```
   - 该脚本将自动下载所需的引擎依赖项和第三方库。

### 2.2 生成项目文件

1. **运行 GenerateProjectFiles.bat**:
   - 输入以下命令生成 Visual Studio 项目文件：
     ```bash
     GenerateProjectFiles.bat
     ```

### 2.3 打开 Visual Studio

1. **打开 Unreal Engine 解决方案**:
   - 在 `C:\UnrealEngine` 目录中，找到 `UE4.sln` 文件，双击打开它。

2. **设置构建配置**:
   - 在 Visual Studio 中，选择 **Development Editor** 作为构建配置。
   - 确保选择 **Win64** 作为平台。

### 2.4 构建引擎

1. **构建引擎**:
   - 在 Visual Studio 中，右键点击解决方案，选择 **Build Solution**（或按 `Ctrl + Shift + B`）。
   - 等待构建完成，这可能需要一些时间。

## 3. 打包引擎

### 3.1 打包引擎

1. **打开命令提示符**:
   - 在 Windows 中，打开命令提示符。

2. **导航到引擎目录**:
   - 输入以下命令以导航到引擎的 `Engine\Build\BatchFiles` 目录：
     ```bash
     cd C:\UnrealEngine\Engine\Build\BatchFiles
     ```

3. **运行打包命令**:
   - 输入以下命令以打包引擎：
     ```bash
     Build.bat -Target=UE4Editor -Platform=Win64 -Configuration=Development
     ```
   - 你可以根据需要更改 `-Configuration` 参数为 `Shipping` 或 `Debug`。

### 3.2 创建可分发的引擎版本

1. **创建引擎的可分发版本**:
   - 在命令提示符中，输入以下命令：
     ```bash
     Build.bat -Target=UE4Editor -Platform=Win64 -Configuration=Shipping -CreateRelease
     ```
   - 这将创建一个可分发的引擎版本，通常会在 `C:\UnrealEngine\Engine\Binaries\Win64` 目录下生成。

## 4. 常见问题

- **构建失败**: 确保所有依赖项已正确下载，并且 Visual Studio 配置正确。
- **缺少 SDK**: 确保 Windows 10 SDK 已正确安装，并在项目设置中配置了正确的路径。
- **权限问题**: 确保以管理员身份运行命令提示符，特别是在安装或构建过程中。

## 5. 参考链接

- [Unreal Engine Documentation](https://docs.unrealengine.com/en-US/index.html)
- [Visual Studio Documentation](https://docs.microsoft.com/en-us/visualstudio/?view=vs-2019)
- [Git Documentation](https://git-scm.com/doc)
