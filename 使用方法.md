### 1. 编译插件

确保你已经按照之前的说明编译了插件，并生成了动态链接库（DLL 或 .so 文件）。

### 2. 加载插件

将生成的库文件复制到你的Kanzi项目目录中。例如，将 `libMyKanziPlugin.so` 或 `MyKanziPlugin.dll` 复制到 `KanziProject/plugins/` 目录。

### 3. 在Kanzi Studio中使用插件

#### 3.1 添加插件

1. 打开Kanzi Studio并加载你的项目。
2. 在项目资源管理器中，右键点击 `Plugins` 文件夹并选择 `Add Plugin...`。
3. 选择你刚刚编译的插件库文件（例如 `libMyKanziPlugin.so` 或 `MyKanziPlugin.dll`）。

#### 3.2 添加自定义节点

1. 在Kanzi Studio中，打开你的场景。
2. 在场景树中，右键点击你希望添加自定义节点的位置，选择 `Add Node` -> `Custom` -> `MyPlugin`。
3. 将你希望旋转的3D模型作为 `MyPlugin` 节点的子节点。

### 4. 运行项目

运行你的Kanzi项目，触摸屏幕并移动手指，你应该会看到模型根据手指的移动进行旋转。

### 示例项目结构

假设你的Kanzi项目目录结构如下：

```
KanziProject/
├── assets/
├── scenes/
├── plugins/
│   └── libMyKanziPlugin.so  # 编译生成的插件库文件
└── project.kanziproj
```

### 示例场景设置

1. 打开 `scenes` 文件夹中的主场景文件（例如 `MainScene.kzscene`）。
2. 在场景树中，右键点击根节点，选择 `Add Node` -> `Custom` -> `MyPlugin`。
3. 将你的3D模型（例如 `MyModel.kzmesh`）拖动到 `MyPlugin` 节点下，使其成为子节点。

### 代码回顾

确保你的 `MyPlugin` 类实现了触摸事件处理和模型旋转逻辑：

```cpp:MyPlugin/MyPlugin.cpp
#include "MyPlugin.hpp"

KZ_IMPLEMENT_NODE(MyPlugin)

MyPlugin::MyPlugin()
{
    // 初始化节点
    m_lastTouchPosition = kz::vec2(0.0f, 0.0f);
    m_rotation = kz::vec3(0.0f, 0.0f, 0.0f);

    // 注册触摸事件监听器
    this->addEventListener<kz::TouchEvent>([this](const kz::TouchEvent& event) {
        if (event.getType() == kz::TouchEvent::TOUCH_MOVE)
        {
            this->onTouchMove(event);
        }
    });
}

MyPlugin::~MyPlugin()
{
    // 清理节点资源
}

void MyPlugin::update(float deltaTime)
{
    // 更新节点逻辑
    this->setRotation(m_rotation);
}

void MyPlugin::onTouchMove(const kz::TouchEvent& event)
{
    kz::vec2 currentTouchPosition = event.getPosition();
    kz::vec2 delta = currentTouchPosition - m_lastTouchPosition;

    // 根据触摸移动的距离来更新旋转角度
    m_rotation.x += delta.y * 0.1f; // 乘以一个系数来调整旋转速度
    m_rotation.y += delta.x * 0.1f;

    m_lastTouchPosition = currentTouchPosition;
}
```