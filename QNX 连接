QNX 连接

adb devices
adb root
adb remount
adb shell
busybox telnet 192.168.118.2
root

FX11密码:
fGh4dalvHp4ubmb2

-------------------------------------------------------------------------

推包
FX11：
adb push *.kzb /data/vendor/nfs/nfs_ota/			#推送kzb文件
T1J:
adb push hmi_cluster /data/vendor/nfs/nfs_ota/      #推送hmi_cluster文件
adb push hmi_early /data/vendor/nfs/nfs_ota/        #推送hmi_early文件

mount -uw /apps/

-------------------------------------------------------------------------

FX11:

cp /otaupdate/*.kzb /apps/cluster/FX11_J1/bin/hmi/FX11_J1/ClusterHMI/ 		 #拷贝Cluster文件
cp /otaupdate/*.kzb /apps/cluster/FX11_J1/bin/hmi/FX11_J1/hvac/       		 #拷贝HVAC文件
cp /otaupdate/*.kzb /apps/cluster/FX11_J1/bin/hmi/FX11_J1/hud/               #拷贝hud文件

sync
bosch_reset
  
T1J:

cp /otaupdate/installPkg/hmi_cluster/* /apps/CHY_T1J/resource/hmi_cluster/   #拷贝cluster文件
cp /otaupdate/installPkg/hmi_early/* /apps/CHY_T1J/resource/hmi_early/       #拷贝early文件
sync
reset

--------------------------------------------------------------------------


FX11刷配置字：

进入QNX;
psis_client -w -1 hut_cfg CFG_DEBUG_MODE 1

PAC配置：
test_psis_car_cfg CFG_CAR_CONFIGURATION 141 2
test_psis_car_cfg CFG_CAR_CONFIGURATION 153 2

--------------------------------------------------------------------------

截图命令:

screenshot -display=1 -file=/otaupdate/installPkg/1.bmp   #TT

screenshot -display=2 -file=/otaupdate/installPkg/2.bmp   #CLUSTER

screenshot -display=3 -file=/otaupdate/installPkg/3.bmp   #MAP

adb pull /data/vendor/nfs/nfs_ota/1.bmp ./

adb pull /data/vendor/nfs/nfs_ota/2.bmp ./

adb pull /data/vendor/nfs/nfs_ota/3.bmp ./

---------------------------------------------------------------------------

T1J连接主机过滤Log;

export CAR_MODLE="CHY_T1J"
export APPS_DIR=/apps/$CAR_MODLE
export LD_LIBRARY_PATH=$APPS_DIR/lib
export PATH=$PATH:$APPS_DIR/bin

cluster_property_dumper | grep 变量名

-----------------------------------------------------------------------------

FX11信号过滤

slog2info -w | grep 变量名
