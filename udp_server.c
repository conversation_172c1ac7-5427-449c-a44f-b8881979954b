#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>

#define PORT 10005
#define BUFFER_SIZE 1024

int main() {
    int server_fd;
    struct sockaddr_in server_addr, client_addr;
    char buffer[BUFFER_SIZE] = "Hello from UDP Server!";
    
    // 创建UDP套接字
    if ((server_fd = socket(AF_INET, SOCK_DGRAM, 0)) < 0) {
        perror("Socket creation failed");
        exit(EXIT_FAILURE);
    }
    
    // 设置服务器地址结构
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = htonl(INADDR_ANY);  // 修改为htonl
    server_addr.sin_port = htons(PORT);
    
    // 设置socket选项，允许地址重用
    int opt = 1;
    if (setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        perror("Setsockopt failed");
        exit(EXIT_FAILURE);
    }
    
    // 绑定套接字到指定端口
    if (bind(server_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("Bind failed");
        exit(EXIT_FAILURE);
    }
    
    printf("UDP Server listening on port %d...\n", PORT);
    
    // 等待接收客户端消息
    socklen_t client_len = sizeof(client_addr);
    char recv_buffer[BUFFER_SIZE];
    
    printf("Waiting for client connection...\n");
    if (recvfrom(server_fd, recv_buffer, BUFFER_SIZE, 0, 
                 (struct sockaddr *)&client_addr, &client_len) < 0) {
        perror("Receive failed");
        exit(EXIT_FAILURE);
    }
    
    printf("Client connected from %s:%d\n", 
           inet_ntoa(client_addr.sin_addr), 
           ntohs(client_addr.sin_port));
    
    // 发送消息到客户端
    if (sendto(server_fd, buffer, strlen(buffer), 0, 
               (struct sockaddr *)&client_addr, client_len) < 0) {
        perror("Send failed");
        exit(EXIT_FAILURE);
    }
    
    printf("Message sent to client\n");
    
    close(server_fd);
    return 0;
}