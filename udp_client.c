#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>

#define PORT 10005
#define BUFFER_SIZE 1024

int main() {
    int client_fd;
    struct sockaddr_in server_addr;
    char buffer[BUFFER_SIZE];
    
    // 创建UDP套接字
    if ((client_fd = socket(AF_INET, SOCK_DGRAM, 0)) < 0) {
        perror("Socket creation failed");
        exit(EXIT_FAILURE);
    }
    
    // 设置服务器地址结构
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);
    
    // 在WSL中使用0.0.0.0或127.0.0.1
    if (inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr) <= 0) {
        perror("Invalid address");
        exit(EXIT_FAILURE);
    }
    
    printf("UDP Client started...\n");
    
    // 发送初始消息到服务器
    const char *init_message = "Hello Server!";
    if (sendto(client_fd, init_message, strlen(init_message), 0,
               (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("Send failed");
        exit(EXIT_FAILURE);
    }
    
    printf("Initial message sent to server\n");
    
    // 接收服务器消息
    socklen_t server_len = sizeof(server_addr);
    int recv_len = recvfrom(client_fd, buffer, BUFFER_SIZE, 0,
                           (struct sockaddr *)&server_addr, &server_len);
    
    if (recv_len < 0) {
        perror("Receive failed");
        exit(EXIT_FAILURE);
    }
    
    buffer[recv_len] = '\0';  // 确保字符串正确终止
    printf("Received message from server: %s\n", buffer);
    
    close(client_fd);
    return 0;
}