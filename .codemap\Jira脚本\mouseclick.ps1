Add-Type -AssemblyName System.Windows.Forms 
Add-Type -AssemblyName System.Drawing 
 
$signature = @' 
[DllImport("user32.dll",CharSet=CharSet.Auto, CallingConvention=CallingConvention.StdCall)] 
public static extern void mouse_event(long dwFlags, long dx, long dy, long cButtons, long dwExtraInfo); 
'@ 
 
$SendMouseClick = Add-Type -memberDefinition $signature -name "Win32MouseEventNew" -namespace Win32Functions -passThru 
 
# Left mouse button down 
$SendMouseClick::mouse_event(0x00000002, 0, 0, 0, 0) 
Start-Sleep -Milliseconds 100 
# Left mouse button up 
$SendMouseClick::mouse_event(0x00000004, 0, 0, 0, 0) 
