import time
import ctypes
from ctypes import wintypes
import sys

# Windows API constants
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004

# Load user32.dll
user32 = ctypes.windll.user32

def click_mouse():
    """Simulate a mouse left click at current cursor position"""
    user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
    time.sleep(0.1)
    user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)

def get_cursor_pos():
    """Get current cursor position"""
    point = wintypes.POINT()
    user32.GetCursorPos(ctypes.byref(point))
    return point.x, point.y

def main():
    print("=" * 40)
    print("         Mouse Auto Click Tool")
    print("=" * 40)
    print()
    print("Instructions:")
    print("1. Move mouse to target position")
    print("2. Press Enter to start clicking")
    print("3. Press Ctrl+C to stop")
    print("=" * 40)
    print()
    
    # Get click interval
    try:
        interval = input("Enter click interval in seconds (default 1): ").strip()
        if not interval:
            interval = 1
        else:
            interval = float(interval)
    except ValueError:
        interval = 1
        print("Invalid input, using default interval of 1 second")
    
    print()
    print("Move mouse to target position and press Enter...")
    input()
    
    # Get initial cursor position
    x, y = get_cursor_pos()
    print(f"Target position: X={x}, Y={y}")
    print(f"Starting auto click every {interval} seconds...")
    print("Press Ctrl+C to stop")
    print()
    
    count = 0
    try:
        while True:
            click_mouse()
            count += 1
            current_time = time.strftime("%H:%M:%S")
            print(f"Click {count} at {current_time}")
            time.sleep(interval)
    except KeyboardInterrupt:
        print(f"\nStopped after {count} clicks")
        print("Script terminated by user")

if __name__ == "__main__":
    main()
