# 第二部分考试：基本工作流（团队协作基础）

## 考试说明
- 考试时间：90分钟
- 总分：100分
- 及格分数：70分
- 考试形式：理论题 + 实践操作题 + 场景分析题

---

## 一、选择题（每题2分，共24分）

### 1. Git Flow中，新功能开发应该从哪个分支创建？
A. master
B. main
C. develop
D. feature

### 2. Fork工作流的主要优势是？
A. 简化分支管理
B. 保护主仓库安全
C. 减少合并冲突
D. 提高开发效率

### 3. 添加上游仓库的正确命令是？
A. `git remote add origin <url>`
B. `git remote add upstream <url>`
C. `git remote set upstream <url>`
D. `git upstream add <url>`

### 4. 合并请求（MR）的标题格式应该是？
A. `<简短描述> (#<任务编号>)`
B. `[<类型>] <简短描述> (#<任务编号>)`
C. `<类型>: <简短描述> #<任务编号>`
D. `#<任务编号> <类型> <简短描述>`

### 5. 以下哪种合并策略适用于功能分支合并到开发分支？
A. Merge commit
B. Squash and merge
C. Cherry-pick
D. Rebase and merge

### 6. MR处于Hold状态时，正确的处理方式是？
A. 立即关闭MR
B. 创建新的MR
C. 修复问题后继续更新同一MR
D. 删除分支重新开始

### 7. 从Fork仓库同步上游更新的正确步骤是？
A. fetch → merge → push
B. pull → merge → push
C. fetch → pull → push
D. merge → fetch → push

### 8. 热修复分支应该从哪个分支创建？
A. develop
B. feature
C. master/main
D. release

### 9. Issue关联格式中，用于Bug修复的关键词是？
A. Closes
B. Fix
C. Resolves
D. Fixes

### 10. 发布分支的主要作用是？
A. 开发新功能
B. 修复紧急bug
C. 版本发布前的准备工作
D. 代码重构

### 11. 以下哪个不是临时分支？
A. feature
B. develop
C. hotfix
D. release

### 12. MR描述模板中必须包含的部分是？
A. 功能描述
B. 实现方案
C. 测试情况
D. 以上都是

---

## 二、判断题（每题2分，共16分）

### 1. 可以直接向项目主仓库推送代码。（）

### 2. 功能分支开发完成后应该立即删除。（）

### 3. 一个MR可以关联多个Issue。（）

### 4. 发布分支可以从feature分支创建。（）

### 5. Fork仓库需要定期与上游仓库同步。（）

### 6. 合并请求的标题可以不包含任务编号。（）

### 7. 热修复分支修复完成后需要同时合并到master和develop分支。（）

### 8. 同一个目标可以存在多个MR。（）

---

## 三、简答题（每题8分，共24分）

### 1. 请详细描述Git Flow的完整工作流程，包括各个分支的作用和合并策略。

### 2. 解释Fork工作流的设置和使用流程，说明为什么要采用这种工作方式。

### 3. 描述合并请求（MR）的完整生命周期，从创建到合并的各个阶段。

---

## 四、场景分析题（每题12分，共24分）

### 场景1：分支管理问题
**背景：**
小张是新入职的开发人员，他需要开发一个用户注册功能。项目采用Git Flow工作流，当前主分支是main，开发分支是develop。

**问题：**
1. 小张应该从哪个分支创建功能分支？为什么？（4分）
2. 功能分支应该如何命名？（假设任务编号是TASK-001）（4分）
3. 开发完成后，应该向哪个分支提交合并请求？（4分）

### 场景2：紧急修复处理
**背景：**
生产环境发现了一个严重的安全漏洞，需要立即修复。当前develop分支有正在开发的功能，不能立即发布。

**问题：**
1. 应该采用什么类型的分支来修复这个问题？从哪个分支创建？（4分）
2. 修复完成后，应该如何处理这个分支？（4分）
3. 这种修复对正在进行的功能开发有什么影响？如何处理？（4分）

---

## 五、实践操作题（共12分）

### 题目：完整的Fork工作流实践

**场景描述：**
你需要为开源项目 `https://gitlab.com/company/awesome-project.git` 贡献代码，实现一个新的搜索功能。

**操作要求：**

1. **Fork仓库设置**（3分）
   - Fork项目到个人空间
   - 克隆个人Fork仓库
   - 添加上游仓库

2. **功能开发**（4分）
   - 创建功能分支 `feature/search-enhancement`
   - 创建搜索功能文件 `search.js`
   - 提交代码，使用规范的提交消息

3. **同步和提交MR**（3分）
   - 同步上游仓库最新代码
   - 推送功能分支到个人仓库
   - 创建合并请求

4. **MR描述**（2分）
   - 编写符合模板要求的MR描述

**请写出完整的命令序列和MR描述：**

```bash
# 第1步：Fork仓库设置
# 在此处写出命令

# 第2步：功能开发
# 在此处写出命令

# 第3步：同步和提交MR
# 在此处写出命令

# 第4步：MR描述
# 在此处写出MR描述内容
```

---

## 六、答案解析

### 选择题答案：
1. C  2. B  3. B  4. B  5. B  6. C  7. A  8. C  9. B  10. C  11. B  12. D

### 判断题答案：
1. ✗  2. ✗  3. ✓  4. ✗  5. ✓  6. ✗  7. ✓  8. ✗

### 简答题参考答案：

**1. Git Flow工作流程：**
- **长期分支**：
  - main/master：生产环境稳定代码
  - develop：开发环境最新代码
- **临时分支**：
  - feature：从develop创建，开发新功能，完成后合并回develop
  - release：从develop创建，版本发布准备，合并到main和develop
  - hotfix：从main创建，紧急修复，合并到main和develop
- **合并策略**：feature使用squash merge，release和hotfix使用merge commit

**2. Fork工作流：**
- **设置**：Fork主仓库→克隆个人仓库→添加上游仓库
- **使用**：创建功能分支→开发→推送到个人仓库→创建MR
- **优势**：保护主仓库安全，便于权限管理，支持开源协作

**3. MR生命周期：**
- 创建：填写标题、描述、关联Issue
- 审查：代码审查、讨论、修改
- 测试：CI/CD检查、手动测试
- 合并：审查通过后合并到目标分支
- 清理：删除功能分支

### 场景分析题参考答案：

**场景1：**
1. 从develop分支创建，因为develop是开发分支，包含最新的开发代码
2. `feature/TASK-001-user-registration`
3. 向develop分支提交合并请求

**场景2：**
1. 创建hotfix分支，从main分支创建
2. 修复完成后合并到main和develop分支，并打标签发布
3. 需要将hotfix的修改同步到develop分支，确保正在开发的功能也包含修复

### 实践操作题参考答案：

```bash
# 第1步：Fork仓库设置
# 1. 在GitLab界面Fork仓库
git clone https://gitlab.com/your-username/awesome-project.git
cd awesome-project
git remote add upstream https://gitlab.com/company/awesome-project.git

# 第2步：功能开发
git checkout -b feature/search-enhancement
echo "// 搜索增强功能" > search.js
git add search.js
git commit -m "feat(search): 添加搜索增强功能"

# 第3步：同步和提交MR
git fetch upstream
git checkout main
git merge upstream/main
git checkout feature/search-enhancement
git rebase main
git push origin feature/search-enhancement
# 在GitLab界面创建MR

# 第4步：MR描述
## 功能描述
实现搜索功能的增强，提供更精确的搜索结果

## 实现方案
- 添加新的搜索算法
- 优化搜索性能
- 支持模糊匹配

## 测试情况
- 单元测试通过
- 手动测试验证功能正常

## 相关链接
- 关联Issue: #123
```

---

## 评分标准

- **选择题/判断题**：答案正确得满分
- **简答题**：按要点给分，逻辑清晰、表述准确
- **场景分析题**：分析合理、解决方案可行
- **实践操作题**：命令正确、流程完整、格式规范

**扣分项：**
- 命令语法错误：-1分/处
- 分支命名不规范：-2分
- MR格式不符合要求：-3分
- 工作流程错误：-5分
