@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    JQL 查询测试工具
echo ========================================
echo.

:: 配置信息
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"
set "API_TOKEN=NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762"
set "API_URL=!JIRA_URL!/rest/api/2/search"

echo 配置信息:
echo JIRA_URL: !JIRA_URL!
echo USERNAME: !USERNAME!
echo API_URL: !API_URL!
echo.

:: 测试1: 最简单的JQL查询
echo [测试1] 最简单的JQL查询
set "JQL1=assignee = currentUser()"
echo JQL: !JQL1!

set "TEMP_FILE1=%TEMP%\jira_test1_%RANDOM%.json"
curl -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" -G "!API_URL!" --data-urlencode "jql=!JQL1!" --data-urlencode "maxResults=1" -o "!TEMP_FILE1!"

echo 响应内容:
if exist "!TEMP_FILE1!" (
    type "!TEMP_FILE1!"
    del "!TEMP_FILE1!"
) else (
    echo 未生成响应文件
)
echo.
echo ----------------------------------------
echo.

:: 测试2: 简化的状态查询
echo [测试2] 简化的状态查询
set "JQL2=assignee = currentUser() AND resolution = Unresolved"
echo JQL: !JQL2!

set "TEMP_FILE2=%TEMP%\jira_test2_%RANDOM%.json"
curl -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" -G "!API_URL!" --data-urlencode "jql=!JQL2!" --data-urlencode "maxResults=1" -o "!TEMP_FILE2!"

echo 响应内容:
if exist "!TEMP_FILE2!" (
    type "!TEMP_FILE2!"
    del "!TEMP_FILE2!"
) else (
    echo 未生成响应文件
)
echo.
echo ----------------------------------------
echo.

:: 测试3: 测试特定状态
echo [测试3] 测试特定状态
set "JQL3=status = \"New Analysis\" AND assignee = currentUser()"
echo JQL: !JQL3!

set "TEMP_FILE3=%TEMP%\jira_test3_%RANDOM%.json"
curl -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" -G "!API_URL!" --data-urlencode "jql=!JQL3!" --data-urlencode "maxResults=1" -o "!TEMP_FILE3!"

echo 响应内容:
if exist "!TEMP_FILE3!" (
    type "!TEMP_FILE3!"
    del "!TEMP_FILE3!"
) else (
    echo 未生成响应文件
)
echo.
echo ----------------------------------------
echo.

:: 测试4: 测试标签查询
echo [测试4] 测试标签查询
set "JQL4=labels = APA AND assignee = currentUser()"
echo JQL: !JQL4!

set "TEMP_FILE4=%TEMP%\jira_test4_%RANDOM%.json"
curl -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" -G "!API_URL!" --data-urlencode "jql=!JQL4!" --data-urlencode "maxResults=1" -o "!TEMP_FILE4!"

echo 响应内容:
if exist "!TEMP_FILE4!" (
    type "!TEMP_FILE4!"
    del "!TEMP_FILE4!"
) else (
    echo 未生成响应文件
)
echo.
echo ----------------------------------------
echo.

:: 测试5: 获取所有可用的状态值
echo [测试5] 获取项目状态信息
set "STATUS_URL=!JIRA_URL!/rest/api/2/status"
echo URL: !STATUS_URL!

set "TEMP_FILE5=%TEMP%\jira_status_%RANDOM%.json"
curl -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" "!STATUS_URL!" -o "!TEMP_FILE5!"

echo 可用状态列表:
if exist "!TEMP_FILE5!" (
    powershell -Command "try { (Get-Content '!TEMP_FILE5!' | ConvertFrom-Json) | ForEach-Object { Write-Output ('状态: ' + $_.name + ' (ID: ' + $_.id + ')') } } catch { Get-Content '!TEMP_FILE5!' }"
    del "!TEMP_FILE5!"
) else (
    echo 未获取到状态信息
)
echo.

echo ========================================
echo 测试完成！
echo.
echo 分析建议:
echo 1. 如果测试1失败，说明基本认证有问题
echo 2. 如果测试1成功但其他失败，说明JQL语法有问题
echo 3. 检查状态名称是否与系统中的完全一致
echo 4. 检查标签名称是否正确
echo ========================================

pause
