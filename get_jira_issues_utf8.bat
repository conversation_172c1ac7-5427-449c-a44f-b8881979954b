@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: Jira问题剩余数量获取脚本 (UTF-8修复版)
:: 作者: Assistant
:: 版本: 2.1
:: 描述: 修复中文乱码问题的版本
:: ========================================

echo.
echo ========================================
echo    Jira 问题剩余数量获取工具
echo ========================================
echo.

:: 配置信息
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

:: 安全地获取密码
echo 请输入您的Jira登录密码：
set /p PASSWORD=密码: 

if "!PASSWORD!"=="" (
    echo [错误] 密码不能为空！
    pause
    exit /b 1
)

echo.
echo [信息] Jira服务器: !JIRA_URL!
echo [信息] 用户名: !USERNAME!
echo.

:: 设置JQL查询
echo 请选择查询类型：
echo 1. 简单查询 - 分配给我的未解决问题
echo 2. 复杂查询 - 特定状态的问题
echo 3. 自定义查询 - 手动输入JQL
echo.
set /p QUERY_TYPE="请选择 (1-3): "

if "!QUERY_TYPE!"=="1" (
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
    echo [信息] 使用简单查询: !JQL_QUERY!
) else if "!QUERY_TYPE!"=="2" (
    :: 使用英文状态名称避免中文编码问题
    echo.
    echo 复杂查询选项：
    echo a. 使用英文状态名称 (推荐)
    echo b. 使用中文状态名称 (可能有编码问题)
    echo.
    set /p STATUS_TYPE="请选择 (a/b): "
    
    if /i "!STATUS_TYPE!"=="a" (
        :: 使用英文状态名称
        set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved AND status in (\"Open\",\"In Progress\",\"Reopened\")"
        echo [信息] 使用英文状态查询: !JQL_QUERY!
    ) else (
        :: 使用中文状态名称，但进行URL编码
        set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
        echo [信息] 使用基础查询避免中文编码问题: !JQL_QUERY!
        echo [提示] 如需特定状态，请在Jira中确认英文状态名称
    )
) else if "!QUERY_TYPE!"=="3" (
    echo.
    echo 请输入JQL查询语句：
    echo 提示：避免使用中文字符，使用英文状态名称
    set /p JQL_QUERY="JQL: "
    if "!JQL_QUERY!"=="" (
        echo [错误] JQL查询不能为空！
        pause
        exit /b 1
    )
    echo [信息] 使用自定义查询: !JQL_QUERY!
) else (
    echo [错误] 无效选择，使用默认查询
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
)

echo.

:: 构建API请求URL
set "API_URL=!JIRA_URL!/rest/api/2/search"
set "TEMP_FILE=%TEMP%\jira_response_%RANDOM%.json"

echo [信息] 正在查询Jira问题...

:: 发送API请求，添加UTF-8编码支持
curl -s -u "!USERNAME!:!PASSWORD!" ^
     -H "Accept: application/json; charset=utf-8" ^
     -H "Content-Type: application/json; charset=utf-8" ^
     -G "!API_URL!" ^
     --data-urlencode "jql=!JQL_QUERY!" ^
     --data-urlencode "maxResults=0" ^
     --data-urlencode "fields=summary" ^
     -o "!TEMP_FILE!" ^
     --connect-timeout 30 ^
     --max-time 60

set CURL_EXIT_CODE=%errorlevel%

:: 检查curl执行结果
if !CURL_EXIT_CODE! neq 0 (
    echo [错误] API请求失败！curl退出代码: !CURL_EXIT_CODE!
    if exist "!TEMP_FILE!" del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 检查响应文件
if not exist "!TEMP_FILE!" (
    echo [错误] 响应文件未生成！
    pause
    exit /b 1
)

:: 检查响应是否为HTML
findstr /i "<html>\|<body>\|<title>" "!TEMP_FILE!" >nul
if !errorlevel! equ 0 (
    echo [错误] 认证失败！返回了HTML页面
    del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 显示响应文件大小
for %%F in ("!TEMP_FILE!") do set "FILE_SIZE=%%~zF"
echo [调试] 响应文件大小: !FILE_SIZE! 字节

:: 检查响应内容是否包含中文乱码
echo [调试] 检查响应内容...
findstr /i "error\|exception" "!TEMP_FILE!" >nul
if !errorlevel! equ 0 (
    echo [警告] 响应中包含错误信息
    echo.
    echo [调试] 响应内容:
    powershell -Command "[System.IO.File]::ReadAllText('!TEMP_FILE!', [System.Text.Encoding]::UTF8)"
    echo.
)

:: 使用PowerShell解析JSON，指定UTF-8编码
echo [信息] 正在解析响应数据...
for /f "delims=" %%i in ('powershell -Command "try { $content = [System.IO.File]::ReadAllText('!TEMP_FILE!', [System.Text.Encoding]::UTF8); ($content | ConvertFrom-Json).total } catch { Write-Output 'JSON_PARSE_ERROR' }"') do (
    set TOTAL_ISSUES=%%i
)

:: 如果解析失败，显示响应内容
if "!TOTAL_ISSUES!"=="JSON_PARSE_ERROR" (
    echo [错误] JSON解析失败！
    echo.
    echo [调试] UTF-8响应内容:
    powershell -Command "[System.IO.File]::ReadAllText('!TEMP_FILE!', [System.Text.Encoding]::UTF8)"
    echo.
    
    :: 保存错误响应
    set "ERROR_FILE=jira_error_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "ERROR_FILE=!ERROR_FILE: =0!"
    copy "!TEMP_FILE!" "!ERROR_FILE!" >nul
    echo 错误响应已保存到: !ERROR_FILE!
    
    del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 清理临时文件
del "!TEMP_FILE!"

:: 检查是否成功获取数据
if "!TOTAL_ISSUES!"=="" (
    echo [错误] 无法获取问题数量！
    pause
    exit /b 1
)

:: 显示结果
echo.
echo ========================================
echo           查询结果
echo ========================================
echo 查询条件: !JQL_QUERY!
echo 问题剩余数量: !TOTAL_ISSUES!
echo 查询时间: %date% %time%
echo ========================================
echo.

:: 保存结果
set /p SAVE_RESULT="是否将结果保存到文件？(y/n): "
if /i "!SAVE_RESULT!"=="y" (
    set "RESULT_FILE=jira_issues_count_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "RESULT_FILE=!RESULT_FILE: =0!"
    
    :: 使用UTF-8编码保存文件
    powershell -Command "$content = @('查询时间: %date% %time%', '查询条件: !JQL_QUERY!', '问题剩余数量: !TOTAL_ISSUES!'); [System.IO.File]::WriteAllLines('!RESULT_FILE!', $content, [System.Text.Encoding]::UTF8)"
    echo [信息] 结果已保存到: !RESULT_FILE!
)

echo.
echo ========================================
echo 脚本执行完成！
echo.
echo 编码修复说明：
echo 1. 已启用UTF-8编码支持
echo 2. 建议使用英文状态名称避免编码问题
echo 3. 如需中文状态，请在Jira中确认准确的英文对应名称
echo ========================================

echo 按任意键退出...
pause >nul
