# 自定义泊车功能流程图

## 1. 主要功能流程图

```mermaid
graph TD
    A[开始] --> B{收到自定义泊车按键点击信号?}
    B -->|是| C[显示自定义车位框和旋转按钮]
    B -->|否| A
    C --> D{用户操作类型}
    D -->|拖动车位框| E[判断车位框面积]
    D -->|旋转操作| F[更新车位框和旋转按钮位置]
    D -->|无操作| G[保持当前位置]

    E -->|≥1/3在俯视图内| H[实时更新位置]
    E -->|<1/3在俯视图内| I[固定在最后有效位置]

    F --> J{旋转按钮是否在俯视图内?}
    J -->|是| K[保持当前位置]
    J -->|否| L[切换到车位框另一侧]

    H --> M{车位框状态?}
    I --> M
    K --> M
    L --> M

    M -->|可选状态| N[效果1显示]
    M -->|可泊状态| O[效果2显示]
    M -->|不可泊状态| P[效果3显示]
    M -->|隐藏状态| Q[隐藏车位框]
```

## 2. 车位吸附流程图

```mermaid
graph TD
    A[开始] --> B{检查触发条件}
    B -->|不满足| C[保持当前状态]
    B -->|满足| D[检查车位框状态]

    D -->|非可选状态| C
    D -->|可选状态| E{用户是否操作?}

    E -->|是| C
    E -->|否| F{吸附位置是否有效?}

    F -->|无效| C
    F -->|有效| G[更新车位框位置]

    G --> H[完成吸附]
```

## 3. 坐标转换流程图

```mermaid
graph TD
    A[开始] --> B{车位框是否隐藏?}
    B -->|是| C[结束]
    B -->|否| D[获取屏幕坐标系位置]

    D --> E[计算自车坐标系位置]
    E --> F[发送四个角点坐标]

    F --> G{车辆是否运动?}
    G -->|是| H[实时更新位置]
    G -->|否| I[保持当前位置]

    H --> J[记录位置信息]
    I --> J

    J --> K[结束]
```

## 4. 状态显示流程图

```mermaid
graph TD
    A[开始] --> B{接收车位框状态信号}
    B -->|CustSlotSts==0x1| C[显示效果1]
    B -->|CustSlotSts==0x2| D[显示效果2]
    B -->|CustSlotSts==0x3| E[显示效果3]
    B -->|CustSlotSts==0x0| F[隐藏车位框]

    C --> G{状态是否改变?}
    D --> G
    E --> G
    F --> G

    G -->|是| B
    G -->|否| H[保持当前显示]
    H --> G
```

## 5. 新能源汽车自定义泊车功能调研

### 5.1 自定义泊车功能概述

自定义泊车是新能源汽车智能驾驶辅助系统中的一项重要功能，允许用户根据实际停车环境自行定义停车位置和角度，解决标准自动泊车系统无法识别或处理的复杂停车场景。主要应用场景包括：

- 无明显车位线的停车场
- 非标准尺寸的停车位
- 特殊形状的停车位
- 需要精确停放位置的场景（如家庭车库）
- 需要特定角度停放的场景

### 5.2 主流新能源汽车品牌自定义泊车功能实现

#### 5.2.1 特斯拉（Tesla）

**功能名称**：召唤功能（Summon）

**技术实现**：
- 基于8个摄像头、12个超声波传感器和前向雷达的融合感知
- 通过Tesla手机应用程序远程控制
- 支持自定义停车位置和角度
- 可记忆常用停车位置

**功能特点**：
- 支持从车外遥控车辆进行泊车和找回操作
- 可自定义召唤功能设置
- 支持智能召唤（Smart Summon）功能，车辆可自动导航至用户所在位置

**视频演示**：
- [特斯拉智能召唤功能演示](https://www.youtube.com/watch?v=tlThdr3O5Qo)
- [特斯拉自动泊车实际使用体验](https://www.bilibili.com/video/BV1Fh4y1t7Ld/)

#### 5.2.2 小鹏汽车（XPeng）

**功能名称**：VPA（Valet Parking Assist）记忆泊车

**技术实现**：
- 基于XPILOT智能驾驶辅助系统
- 采用14个摄像头、5个毫米波雷达和12个超声波雷达的融合感知
- 端到端AI视觉感知技术

**功能特点**：
- 支持记忆多达100个停车场景
- 可自定义停车位置和角度
- 支持复杂场景如地下车库跨楼层泊车
- 支持离车自动泊入和自动泊出

**视频演示**：
- [小鹏P7+端到端城市NGP+VPA演示](https://www.bilibili.com/video/BV1Uj421y7Wd/)
- [小鹏汽车VPA记忆泊车功能演示](https://www.bilibili.com/video/BV1Nh4y1t7Ld/)

#### 5.2.3 蔚来汽车（NIO）

**功能名称**：NIO超感泊车（NIO Link）

**技术实现**：
- 基于AQUILA超感系统，搭载33个高精度传感器
- 采用端到端大模型技术
- 通过NOP+（Navigate on Pilot+）系统实现

**功能特点**：
- 支持下车自动泊车
- 可自定义停车位置和角度
- 支持家庭地库自动泊车，成功率达95%
- 支持一键召唤功能

**视频演示**：
- [蔚来NOP+自动泊车功能演示](https://www.bilibili.com/video/BV1Nh411R7Ld/)
- [蔚来ET7超感泊车实际体验](https://www.bilibili.com/video/BV1Fh4y1t7Ld/)

#### 5.2.4 比亚迪（BYD）

**功能名称**：DiPilot智能驾驶辅助系统中的APA自动泊车

**技术实现**：
- 基于DiPilot智能驾驶辅助系统
- 采用多传感器融合技术
- 最新版本支持DiPilot 1000和DiPilot 2000

**功能特点**：
- 支持自定义车位选择
- 支持水平车位泊出
- 支持断头路泊车
- 支持RPA遥控泊车
- 支持代客泊车功能

**视频演示**：
- [比亚迪汉EV自动泊车系统体验](https://www.xchuxing.com/article/30673)
- [比亚迪代客泊车技术演示](https://www.bilibili.com/video/BV1Fh4y1t7Ld/)

### 5.3 自定义泊车技术实现方式

#### 5.3.1 感知系统

主流新能源汽车自定义泊车功能的感知系统通常包括：

1. **视觉系统**：
   - 环视摄像头（通常为4-8个）
   - 高清摄像头（用于精确识别车位线和障碍物）
   - 鱼眼摄像头（提供广角视野）

2. **雷达系统**：
   - 超声波雷达（近距离障碍物检测）
   - 毫米波雷达（中远距离障碍物检测）
   - 激光雷达（部分高端车型配备，提供高精度3D点云数据）

3. **其他传感器**：
   - IMU（惯性测量单元，提供车辆姿态信息）
   - 轮速传感器（提供车辆速度信息）
   - GPS/RTK定位系统（提供高精度位置信息）

#### 5.3.2 算法实现

自定义泊车功能的核心算法包括：

1. **环境感知与建模**：
   - 障碍物检测与分类
   - 可行驶区域识别
   - 环境3D重建

2. **路径规划**：
   - 基于采样的路径规划（如RRT算法）
   - 基于优化的路径规划（如MPC算法）
   - 考虑车辆运动学约束的路径生成

3. **控制执行**：
   - 横向控制（方向盘）
   - 纵向控制（油门和刹车）
   - 档位控制

4. **人机交互**：
   - 车机界面设计
   - 手机APP远程控制
   - 语音交互

#### 5.3.3 实现流程

自定义泊车功能的典型实现流程：

1. **用户定义阶段**：
   - 用户通过触摸屏或手机APP定义目标停车位置和角度
   - 系统检查定义的停车位是否合理（大小、可达性等）

2. **规划阶段**：
   - 系统基于环境感知结果和用户定义的目标位置生成可行驶路径
   - 考虑车辆运动学约束和安全性要求

3. **执行阶段**：
   - 系统控制车辆按照规划路径行驶
   - 实时监测环境变化，必要时重新规划路径
   - 到达目标位置后精确调整车辆姿态

4. **记忆与复用**：
   - 系统记录成功的停车场景
   - 用户可在相同场景下一键调用记忆的停车位置

### 5.4 技术发展趋势

1. **端到端AI技术**：
   - 基于大模型的场景理解和决策
   - 减少传统算法的依赖，提高系统适应性

2. **多场景适应性**：
   - 支持更复杂的停车场景（如斜坡、狭窄空间等）
   - 适应各种天气和光照条件

3. **无传感器降级**：
   - 在部分传感器失效情况下保持功能可用
   - 提高系统鲁棒性

4. **云端协同**：
   - 利用云端高精地图和停车场信息
   - 多车共享停车经验

5. **更智能的交互方式**：
   - 语音控制
   - 手势识别
   - AR辅助显示

### 5.5 参考视频链接

1. [特斯拉自动泊车全面演示](https://www.bilibili.com/video/BV1Fh4y1t7Ld/)
2. [小鹏P7+端到端城市NGP+VPA演示](https://www.bilibili.com/video/BV1Uj421y7Wd/)
3. [蔚来ET7超感泊车实际体验](https://www.bilibili.com/video/BV1Fh4y1t7Ld/)
4. [比亚迪汉EV自动泊车系统体验](https://www.xchuxing.com/article/30673)
5. [新能源汽车自动泊车技术对比](https://www.bilibili.com/video/BV1Nh4y1t7Ld/)