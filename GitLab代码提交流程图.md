# GitLab代码提交流程图

```mermaid
flowchart TD
    %% 主要流程节点
    Start([开始]) --> ForkRepo["从主仓库Fork出个人仓库<br/>在GitLab项目页点击Fork按钮"]
    ForkRepo --> CloneRepo["克隆个人Fork的仓库<br/>git clone https://gitlab.com/your-username/project-name.git"]
    CloneRepo --> AddUpstream["添加上游仓库作为远程仓库<br/>git remote add upstream https://gitlab.com/organization/project-name.git"]

    %% 同步上游仓库
    AddUpstream --> SyncUpstream["同步上游仓库<br/>git fetch upstream<br/>git checkout main<br/>git merge upstream/main<br/>git push origin main"]

    %% 分支创建
    SyncUpstream --> BranchType{选择分支类型}
    BranchType -->|新功能| FeatureBranch["创建功能分支<br/>git checkout -b feature/123-user-login"]
    BranchType -->|Bug修复| BugfixBranch["创建修复分支<br/>git checkout -b bugfix/456-fix-memory-leak"]
    BranchType -->|紧急修复| HotfixBranch["从master创建热修复分支<br/>git checkout -b hotfix/789-critical-security-issue"]
    BranchType -->|版本发布| ReleaseBranch["从develop创建发布分支<br/>git checkout -b release/v1.2.0"]

    %% 开发工作
    FeatureBranch --> DevWork[进行开发工作]
    BugfixBranch --> DevWork
    HotfixBranch --> DevWork
    ReleaseBranch --> DevWork

    %% 大文件处理
    DevWork --> CheckLargeFiles{是否有大文件?}
    CheckLargeFiles -->|是| SetupLFS["设置Git LFS<br/>git lfs install"]
    CheckLargeFiles -->|否| LocalTest

    SetupLFS --> TrackLFS["跟踪大文件<br/>git lfs track '*.psd'<br/>git lfs track '*.zip'<br/>git lfs track '*.mp4'"]
    TrackLFS --> CommitLFSConfig["提交LFS配置<br/>git add .gitattributes<br/>git commit -m 'chore: 配置Git LFS跟踪大文件'"]
    CommitLFSConfig --> LocalTest

    %% 本地测试
    LocalTest[本地测试] --> CommitChanges

    %% 提交更改
    CommitChanges --> CommitMsg["按规范提交代码<br/><类型>(<范围>): <主题><br/>例如: feat(login): 实现用户登录功能"]

    %% 提交类型
    CommitMsg --> CommitType{提交类型}
    CommitType -->|新功能| CommitFeat["feat: 新功能"]
    CommitType -->|Bug修复| CommitFix["fix: Bug修复"]
    CommitType -->|文档更新| CommitDocs["docs: 文档更新"]
    CommitType -->|代码格式| CommitStyle["style: 代码格式"]
    CommitType -->|重构| CommitRefactor["refactor: 重构"]
    CommitType -->|性能优化| CommitPerf["perf: 性能优化"]
    CommitType -->|测试| CommitTest["test: 测试"]
    CommitType -->|构建系统| CommitBuild["build: 构建系统"]
    CommitType -->|CI/CD| CommitCI["ci: CI/CD"]
    CommitType -->|其他| CommitChore["chore: 其他"]

    CommitFeat --> PushChanges
    CommitFix --> PushChanges
    CommitDocs --> PushChanges
    CommitStyle --> PushChanges
    CommitRefactor --> PushChanges
    CommitPerf --> PushChanges
    CommitTest --> PushChanges
    CommitBuild --> PushChanges
    CommitCI --> PushChanges
    CommitChore --> PushChanges

    %% 推送更改
    PushChanges["推送到个人Fork仓库<br/>git push origin 分支名称"] --> CreateMR

    %% 创建合并请求
    CreateMR["创建合并请求(MR)<br/>源分支: 个人Fork仓库的功能分支<br/>目标分支: 项目主仓库的develop/master分支"] --> MRTemplate

    MRTemplate["填写MR描述模板<br/>- 功能描述<br/>- 实现方案<br/>- 测试情况<br/>- 相关链接<br/>- 截图(如有)"] --> AssignReviewer

    %% 代码审查
    AssignReviewer["指定代码审查者"] --> CodeReview["代码审查<br/>- 代码规范<br/>- 功能实现<br/>- 安全问题<br/>- 性能优化<br/>- 测试覆盖"]

    CodeReview --> ReviewResult{审查结果}
    ReviewResult -->|需要修改| ProvideComments["审查者提出修改建议"]
    ProvideComments --> FixIssues["开发者根据建议修改"]
    FixIssues --> CommitChanges

    %% 合并策略
    ReviewResult -->|通过| ApproveChanges["审查者批准合并"]
    ApproveChanges --> ProjectOwner["项目负责人执行最终合并"]

    ProjectOwner --> MergeStrategy{选择合并策略}
    MergeStrategy -->|功能分支到develop| SquashMerge["Squash and merge"]
    MergeStrategy -->|发布分支到master| MergeCommit["Merge commit"]
    MergeStrategy -->|热修复分支| CherryPick["Cherry-pick到master和develop"]

    SquashMerge --> MergeDone[合并完成]
    MergeCommit --> MergeDone
    CherryPick --> MergeDone

    %% 版本标签
    MergeDone --> CheckRelease{是否为发布分支?}
    CheckRelease -->|是| CreateTag["创建版本标签<br/>git tag -a v1.2.0 -m '版本1.2.0发布'<br/>git push origin v1.2.0"]
    CheckRelease -->|否| CleanUp
    CreateTag --> CleanUp

    %% 清理工作
    CleanUp["清理工作<br/>- 删除已合并的功能分支<br/>- 同步Fork仓库"] --> SyncAfterMerge["同步Fork仓库与上游<br/>git fetch upstream<br/>git checkout main<br/>git merge upstream/main<br/>git push origin main"]

    SyncAfterMerge --> End([结束])

    %% 子模块处理（如果有）
    subgraph 子模块处理
        SubmoduleCheck{项目包含子模块?} -->|是| UpdateSubmodule["更新子模块<br/>git submodule update --init --recursive"]
        SubmoduleCheck -->|否| SkipSubmodule[跳过子模块处理]

        UpdateSubmodule --> SubmoduleChanges{子模块需要修改?}
        SubmoduleChanges -->|是| EnterSubmodule["进入子模块目录<br/>cd path/to/submodule"]
        SubmoduleChanges -->|否| SkipSubmodule

        EnterSubmodule --> SubmoduleBranch["在子模块中创建分支<br/>git checkout -b feature/submodule-feature"]
        SubmoduleBranch --> SubmoduleWork["在子模块中进行开发"]
        SubmoduleWork --> SubmoduleCommit["提交子模块更改<br/>git add .<br/>git commit -m 'feat: 子模块功能'"]
        SubmoduleCommit --> SubmodulePush["推送子模块更改<br/>git push origin feature/submodule-feature"]
        SubmodulePush --> SubmoduleMR["为子模块创建MR"]
        SubmoduleMR --> ReturnToMain["返回主项目<br/>cd ../"]
        ReturnToMain --> UpdateSubmoduleRef["更新主项目中的子模块引用<br/>git add path/to/submodule<br/>git commit -m 'chore: 更新子模块'"]
    end

    %% LFS大文件处理（详细）
    subgraph LFS大文件处理
        LFSCheck{需要处理大文件?} -->|是| LFSInstall["安装Git LFS<br/>git lfs install"]
        LFSCheck -->|否| SkipLFS[跳过LFS处理]

        LFSInstall --> LFSTrack["跟踪大文件类型<br/>git lfs track '*.psd'<br/>git lfs track '*.zip'"]
        LFSTrack --> LFSCommitAttr["提交.gitattributes<br/>git add .gitattributes<br/>git commit -m 'chore: 配置Git LFS'"]

        LFSCommitAttr --> LFSAddFiles["添加大文件<br/>git add large-file.psd"]
        LFSAddFiles --> LFSCommitFiles["提交大文件<br/>git commit -m 'feat: 添加设计文件'"]
        LFSCommitFiles --> LFSPush["推送LFS文件<br/>git push origin feature/branch"]

        LFSPush --> LFSStatus["检查LFS状态<br/>git lfs status"]
    end
```
