@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Jira 认证方式测试工具
echo ========================================
echo.

set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"
set "API_TOKEN=NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762"

:: 测试1: 基本认证 (用户名:API Token)
echo [测试1] 基本认证 (用户名:API Token)
set "TEST_URL=!JIRA_URL!/rest/api/2/myself"
curl -v -u "!USERNAME!:!API_TOKEN!" "!TEST_URL!" 2>&1 | findstr /i "HTTP\|401\|403\|200\|302\|Location"
echo.

:: 测试2: 基本认证 (用户名:密码) - 注意：这只是测试，不要使用真实密码
echo [测试2] 检查是否需要密码认证而不是API Token
echo 注意：如果您的Jira不支持API Token，可能需要使用密码
echo 但出于安全考虑，我们不在脚本中测试密码认证
echo.

:: 测试3: 检查API是否完全可用
echo [测试3] 检查API端点是否存在
curl -v -I "!JIRA_URL!/rest/api/2/" 2>&1 | findstr /i "HTTP\|404\|200\|403"
echo.

:: 测试4: 检查是否需要特殊头部
echo [测试4] 使用额外的认证头部
curl -v -u "!USERNAME!:!API_TOKEN!" ^
     -H "Accept: application/json" ^
     -H "Content-Type: application/json" ^
     -H "X-Atlassian-Token: no-check" ^
     "!TEST_URL!" 2>&1 | findstr /i "HTTP\|401\|403\|200\|302"
echo.

:: 测试5: 检查重定向
echo [测试5] 检查重定向情况
curl -v -L -u "!USERNAME!:!API_TOKEN!" "!TEST_URL!" 2>&1 | findstr /i "HTTP\|Location\|301\|302\|401\|403\|200"
echo.

:: 测试6: 检查是否是HTTPS证书问题
echo [测试6] 忽略SSL证书验证
curl -v -k -u "!USERNAME!:!API_TOKEN!" "!TEST_URL!" 2>&1 | findstr /i "HTTP\|401\|403\|200\|SSL"
echo.

echo ========================================
echo 分析建议:
echo.
echo 如果看到:
echo - HTTP/1.1 200 OK: 认证成功
echo - HTTP/1.1 401 Unauthorized: 认证失败，检查用户名/API Token
echo - HTTP/1.1 403 Forbidden: 权限不足，可能API被禁用
echo - HTTP/1.1 302 Found: 被重定向，通常到登录页面
echo - HTTP/1.1 404 Not Found: API端点不存在
echo.
echo 解决方案:
echo 1. 如果是401错误: 重新生成API Token
echo 2. 如果是403错误: 联系管理员开启API权限
echo 3. 如果是302重定向: 可能需要不同的认证方式
echo 4. 如果是404错误: 检查Jira版本和API路径
echo ========================================

pause
