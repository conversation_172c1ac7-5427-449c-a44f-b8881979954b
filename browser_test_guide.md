# Jira API 浏览器测试指南

由于脚本返回HTML页面而不是JSON，我们需要通过浏览器来诊断问题。

## 步骤1：测试基本API访问

在浏览器中依次访问以下URL：

### 1. 测试serverInfo端点（无需认证）
```
https://globaljira.geely.com/rest/api/2/serverInfo
```
**期望结果**：应该返回JSON格式的服务器信息
**如果返回**：HTML登录页面，说明API被保护或禁用

### 2. 测试API根路径
```
https://globaljira.geely.com/rest/api/2/
```
**期望结果**：API文档或JSON响应
**如果返回**：404错误，说明API路径不正确

### 3. 测试认证端点
```
https://globaljira.geely.com/rest/api/2/myself
```
**期望结果**：提示输入用户名密码，或返回认证错误
**如果返回**：直接跳转到登录页面

## 步骤2：检查Jira版本和配置

### 1. 访问Jira主页
```
https://globaljira.geely.com
```
查看页面底部的版本信息

### 2. 检查API文档
```
https://globaljira.geely.com/rest/api/2/
```
或
```
https://globaljira.geely.com/plugins/servlet/restbrowser
```

## 步骤3：测试认证方式

### 方法1：浏览器基本认证测试
1. 在浏览器中访问：`https://globaljira.geely.com/rest/api/2/myself`
2. 当提示输入用户名密码时：
   - 用户名：`<EMAIL>`
   - 密码：`NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762`（API Token）

### 方法2：开发者工具测试
1. 按F12打开开发者工具
2. 切换到Console标签
3. 执行以下JavaScript代码：

```javascript
// 测试API访问
fetch('https://globaljira.geely.com/rest/api/2/myself', {
    method: 'GET',
    headers: {
        'Authorization': 'Basic ' + btoa('<EMAIL>:NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762'),
        'Accept': 'application/json'
    }
})
.then(response => {
    console.log('Status:', response.status);
    console.log('Headers:', response.headers);
    return response.text();
})
.then(data => {
    console.log('Response:', data);
})
.catch(error => {
    console.error('Error:', error);
});
```

## 可能的问题和解决方案

### 问题1：所有API请求都返回HTML登录页面
**原因**：
- API Token认证不被支持
- 需要先通过Web界面登录
- API访问被管理员禁用

**解决方案**：
1. 联系IT部门确认API访问权限
2. 检查是否需要使用密码而不是API Token
3. 确认API是否被启用

### 问题2：API端点返回404
**原因**：
- Jira版本较老，API路径不同
- API被禁用或移除

**解决方案**：
1. 尝试旧版API路径：`/rest/api/latest/`
2. 检查Jira版本和对应的API文档

### 问题3：CORS错误
**原因**：
- 浏览器跨域限制

**解决方案**：
- 这是正常的，说明API端点存在
- 需要使用服务器端工具（如curl）访问

## 下一步行动

根据浏览器测试结果：

1. **如果浏览器中API返回JSON** → curl配置问题，检查认证方式
2. **如果浏览器中也返回HTML** → API被禁用或需要特殊认证
3. **如果提示输入密码** → 尝试使用密码而不是API Token
4. **如果返回404** → 检查API路径和Jira版本

请按照上述步骤测试，并告诉我每个步骤的具体结果。
