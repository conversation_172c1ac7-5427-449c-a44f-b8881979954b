# 使用了UE的版本控制git之后，打包一直失败，报错如下：

```bash
********** BUILD COMMAND STARTED **********
UATHelper: Packaging (Windows (64-bit)): Running: 
`D:\UE_4.26\Engine\Binaries\DotNET\UnrealBuildTool.exe HIA Win64 Development -Project=E:\UEProject\HIA\HIA.uproject E:\UEProject\HIA\HIA.uproject -NoUBTMakefiles -remoteini="E:\UEProject\HIA" -skipdeploy -Manifest=E:\UEProject\HIA\Intermediate\Build\Manifest.xml -NoHotReload -log="C:\Users\<USER>\AppData\Roaming\Unreal Engine\AutomationTool\Logs\D+UE_4.26\UBT-HIA-Win64-Development.txt"`
UATHelper: Packaging (Windows (64-bit)): Using 'git status' to determine working set for adaptive non-unity build (E:\UEProject\HIA).
UATHelper: Packaging (Windows (64-bit)): 
UATHelper: Packaging (Windows (64-bit)): 
UATHelper: Packaging (Windows (64-bit)): 由于 Exception.ToString() 失败，因此无法打印异常字符串。
UATHelper: Packaging (Windows (64-bit)): Took 2.0373082s to run UnrealBuildTool.exe, ExitCode=-532462766
UATHelper: Packaging (Windows (64-bit)): UnrealBuildTool failed. See log for more details. 
`(C:\Users\<USER>\AppData\Roaming\Unreal Engine\AutomationTool\Logs\D+UE_4.26\UBT-HIA-Win64-Development.txt)`
UATHelper: Packaging (Windows (64-bit)): AutomationTool exiting with ExitCode=-532462766 (-532462766)
UATHelper: Packaging (Windows (64-bit)): BUILD FAILED
PackagingResults: Error: Unknown Error
```

网上搜了一下，发现都是说将之前里面的关于git文件全部删除，但这样就没办法版本控制了，所以发现了新的解决方法：

在`BuildConfiguration.xml`将如下配置加上即可：

```xml
<SourceFileWorkingSet> 
    <Provider>None</Provider> 
    <RepositoryPath></RepositoryPath> 
    <GitPath></GitPath> 
</SourceFileWorkingSet>
```

而xml所在目录：

除添加到 `Config/UnrealBuildTool` 文件夹中已生成UE4项目外，虚幻编译工具还会从以下位置（Windows系统）的XML配置文件读取设置：

```bash
Engine/Saved/UnrealBuildTool/BuildConfiguration.xml
User Folder/AppData/Roaming/Unreal Engine/UnrealBuildTool/BuildConfiguration.xml
My Documents/Unreal Engine/UnrealBuildTool/BuildConfiguration.xml
```

如果是Linux和Mac，则会从以下路径读取：

```bash
/Users//.config//Unreal Engine/UnrealBuildTool/BuildConfiguration.xml
/Users//Unreal Engine/UnrealBuildTool/BuildConfiguration.xml
```

参考链接：
- [Epic Games Documentation](https://dev.epicgames.com/documentation/zh-cn/unreal-engine/build-configuration?application_version=4.27)
- [Unreal Engine Documentation](https://docs.unrealengine.com/en-US/ProductionPipelines/BuildTools/UnrealBuildTool/BuildConfiguration/index.html)
