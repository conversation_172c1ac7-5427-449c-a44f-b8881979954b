@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    新API Token测试工具
echo ========================================
echo.

set "JIRA_URL=https://globaljira.geely.com"

echo 请输入您的认证信息：
echo.
set /p USERNAME="用户名（通常是邮箱）: "
set /p API_TOKEN="新的API Token: "

if "!USERNAME!"=="" (
    echo 错误：用户名不能为空
    pause
    exit /b 1
)

if "!API_TOKEN!"=="" (
    echo 错误：API Token不能为空
    pause
    exit /b 1
)

echo.
echo 测试认证信息...
echo 用户名: !USERNAME!
echo API Token: !API_TOKEN:~0,10!...（已隐藏）
echo.

:: 测试认证
echo [测试1] 测试基本认证
set "TEST_URL=!JIRA_URL!/rest/api/2/myself"
set "TEMP_FILE=%TEMP%\auth_test_%RANDOM%.json"

curl -s -u "!USERNAME!:!API_TOKEN!" "!TEST_URL!" -o "!TEMP_FILE!"
set "CURL_CODE=!errorlevel!"

echo curl退出代码: !CURL_CODE!

if exist "!TEMP_FILE!" (
    echo.
    echo 响应内容:
    type "!TEMP_FILE!"
    
    :: 检查是否是JSON
    findstr /i "displayName\|emailAddress\|accountId" "!TEMP_FILE!" >nul
    if !errorlevel! equ 0 (
        echo.
        echo ========================================
        echo 🎉 认证成功！API Token有效
        echo ========================================
        
        :: 保存有效的配置
        echo 是否保存这个有效的配置？
        set /p SAVE_CONFIG="保存配置到文件？(y/n): "
        if /i "!SAVE_CONFIG!"=="y" (
            echo # Jira配置文件 - 有效配置 > jira_config_working.txt
            echo JIRA_URL=!JIRA_URL! >> jira_config_working.txt
            echo USERNAME=!USERNAME! >> jira_config_working.txt
            echo API_TOKEN=!API_TOKEN! >> jira_config_working.txt
            echo JQL_QUERY=assignee = currentUser() AND resolution = Unresolved >> jira_config_working.txt
            echo.
            echo 配置已保存到 jira_config_working.txt
        )
        
        :: 测试搜索API
        echo.
        echo [测试2] 测试搜索API
        set "SEARCH_URL=!JIRA_URL!/rest/api/2/search"
        set "SEARCH_FILE=%TEMP%\search_test_%RANDOM%.json"
        
        curl -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" -G "!SEARCH_URL!" --data-urlencode "jql=assignee = currentUser()" --data-urlencode "maxResults=1" -o "!SEARCH_FILE!"
        
        if exist "!SEARCH_FILE!" (
            echo 搜索API响应:
            type "!SEARCH_FILE!"
            
            :: 尝试解析总数
            for /f "delims=" %%i in ('powershell -Command "try { (Get-Content '!SEARCH_FILE!' | ConvertFrom-Json).total } catch { Write-Output 'ERROR' }"') do (
                set TOTAL=%%i
            )
            
            if "!TOTAL!" neq "ERROR" (
                echo.
                echo ========================================
                echo 🎉 搜索API也正常！
                echo 分配给您的问题总数: !TOTAL!
                echo ========================================
            )
            
            del "!SEARCH_FILE!"
        )
        
    ) else (
        echo.
        echo ❌ 认证失败：响应不是有效的用户信息
        findstr /i "401\|unauthorized\|login" "!TEMP_FILE!" >nul
        if !errorlevel! equ 0 (
            echo 检测到401错误，请检查：
            echo 1. 用户名是否正确
            echo 2. API Token是否有效
            echo 3. 是否需要使用密码而不是API Token
        )
    )
    
    del "!TEMP_FILE!"
) else (
    echo 未生成响应文件，可能是网络问题
)

echo.
echo ========================================
echo 如果认证仍然失败，可能的原因：
echo 1. 您的Jira不支持API Token认证
echo 2. 需要使用密码而不是API Token
echo 3. 账户被锁定或权限不足
echo 4. 需要联系IT部门开启API访问权限
echo ========================================

pause
