@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Jira 替代连接测试
echo ========================================
echo.

:: 可能的替代URL
set "URL1=https://globaljira.geely.com"
set "URL2=http://globaljira.geely.com"
set "URL3=https://jira.geely.com"
set "URL4=http://jira.geely.com"
set "URL5=https://globaljira.geely.com:8080"
set "URL6=http://globaljira.geely.com:8080"

echo 测试多个可能的Jira地址...
echo.

for %%i in (1 2 3 4 5 6) do (
    call set "TEST_URL=%%URL%%i%%"
    echo [测试%%i] 测试URL: !TEST_URL!
    
    :: 提取主机名
    for /f "tokens=3 delims=/" %%a in ("!TEST_URL!") do (
        for /f "tokens=1 delims=:" %%b in ("%%a") do set "HOST=%%b"
    )
    
    echo 主机名: !HOST!
    
    :: 测试DNS解析
    nslookup !HOST! >nul 2>&1
    if !errorlevel! equ 0 (
        echo DNS解析: 成功
        
        :: 测试HTTP连接
        curl -s --connect-timeout 5 --max-time 10 "!TEST_URL!/rest/api/2/serverInfo" -o nul 2>nul
        if !errorlevel! equ 0 (
            echo HTTP连接: 成功
            echo [发现可用URL] !TEST_URL!
            
            :: 测试完整API
            echo 测试完整API访问...
            curl -s -u "<EMAIL>:NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762" "!TEST_URL!/rest/api/2/myself" -o nul
            if !errorlevel! equ 0 (
                echo API认证: 成功
                echo.
                echo ========================================
                echo 找到可用的Jira地址: !TEST_URL!
                echo ========================================
                
                :: 更新配置文件
                echo 是否更新配置文件？
                set /p UPDATE="更新jira_config_new.txt中的URL？(y/n): "
                if /i "!UPDATE!"=="y" (
                    echo # Jira配置文件 > jira_config_new.txt
                    echo # 自动检测到的可用URL >> jira_config_new.txt
                    echo. >> jira_config_new.txt
                    echo JIRA_URL=!TEST_URL! >> jira_config_new.txt
                    echo USERNAME=<EMAIL> >> jira_config_new.txt
                    echo API_TOKEN=NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762 >> jira_config_new.txt
                    echo JQL_QUERY=assignee = currentUser() ^&^& resolution = Unresolved >> jira_config_new.txt
                    echo 配置文件已更新！
                )
                goto :found
            ) else (
                echo API认证: 失败
            )
        ) else (
            echo HTTP连接: 失败
        )
    ) else (
        echo DNS解析: 失败
    )
    echo.
)

echo 未找到可用的Jira地址
echo.
echo 建议:
echo 1. 检查VPN连接
echo 2. 确认公司网络连接
echo 3. 联系IT部门确认正确的Jira地址
echo 4. 可能的内网地址格式:
echo    - http://jira.internal.geely.com
echo    - https://jira-prod.geely.com
echo    - http://10.x.x.x:8080 (内网IP)
goto :end

:found
echo.
echo 现在可以运行主脚本了！

:end
pause
