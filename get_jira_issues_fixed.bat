@echo off
setlocal enabledelayedexpansion

:: ========================================
:: Jira问题剩余数量获取脚本 (修复版)
:: 作者: Assistant
:: 版本: 1.1
:: 描述: 通过Jira REST API获取问题剩余数量
:: ========================================

echo.
echo ========================================
echo    Jira 问题剩余数量获取工具
echo ========================================
echo.

:: 直接设置配置（避免文件读取的复杂性）
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"
set "API_TOKEN=NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762"

:: 设置JQL查询（使用转义字符处理特殊字符）
set "JQL_QUERY=status in (\"New Analysis\",\"Supplier Inbox\",\"Rejected\") AND resolution = Unresolved AND labels in (APA) AND assignee in (e-pengxiaolong) ORDER BY updated DESC"

echo [信息] Jira服务器: !JIRA_URL!
echo [信息] 用户名: !USERNAME!
echo [信息] JQL查询: !JQL_QUERY!
echo.

:: 检查curl是否可用
curl --version >nul 2>&1
if errorlevel 1 (
    echo [错误] curl 命令不可用！请确保已安装curl或使用Windows 10/11内置版本
    pause
    exit /b 1
)

:: 构建API请求URL
set "API_URL=!JIRA_URL!/rest/api/2/search"

:: 创建临时文件存储响应
set "TEMP_FILE=%TEMP%\jira_response_%RANDOM%.json"

echo [信息] 正在查询Jira问题...

:: 发送API请求
curl -s -u "!USERNAME!:!API_TOKEN!" ^
     -H "Accept: application/json" ^
     -G "!API_URL!" ^
     --data-urlencode "jql=!JQL_QUERY!" ^
     --data-urlencode "maxResults=0" ^
     --data-urlencode "fields=summary" ^
     -o "!TEMP_FILE!" ^
     --connect-timeout 30 ^
     --max-time 60

set CURL_EXIT_CODE=%errorlevel%
echo [调试] curl退出代码: !CURL_EXIT_CODE!

:: 检查curl执行结果
if !CURL_EXIT_CODE! neq 0 (
    echo [错误] API请求失败！curl退出代码: !CURL_EXIT_CODE!
    echo.
    echo 常见错误代码说明:
    echo   6  - 无法解析主机名
    echo   7  - 无法连接到服务器
    echo   22 - HTTP错误（如401未授权、404未找到等）
    echo   28 - 操作超时
    echo   35 - SSL连接错误
    echo.
    
    if exist "!TEMP_FILE!" (
        echo.
        echo [调试] 响应内容:
        type "!TEMP_FILE!"
        del "!TEMP_FILE!"
    )
    pause
    exit /b 1
)

:: 检查响应文件是否存在
if not exist "!TEMP_FILE!" (
    echo [错误] 响应文件未生成！
    pause
    exit /b 1
)

:: 显示响应文件大小（用于调试）
for %%F in ("!TEMP_FILE!") do set "FILE_SIZE=%%~zF"
echo [调试] 响应文件大小: !FILE_SIZE! 字节

:: 如果文件很小，可能是错误响应
if !FILE_SIZE! lss 50 (
    echo [警告] 响应文件很小，可能是错误响应
    echo [调试] 响应内容:
    type "!TEMP_FILE!"
    echo.
)

:: 解析JSON响应获取总数
echo [信息] 正在解析响应数据...

:: 首先显示响应内容用于调试
echo.
echo [调试] 响应内容（前1000字符）:
echo ----------------------------------------
powershell -Command "Get-Content '!TEMP_FILE!' -Raw | Select-Object -First 1 | ForEach-Object { $_.Substring(0, [Math]::Min(1000, $_.Length)) }"
echo ----------------------------------------
echo.

:: 使用PowerShell解析JSON（Windows内置）
for /f "delims=" %%i in ('powershell -Command "try { (Get-Content '!TEMP_FILE!' | ConvertFrom-Json).total } catch { Write-Output 'JSON_PARSE_ERROR' }"') do (
    set TOTAL_ISSUES=%%i
)

:: 检查是否成功获取数据
if "!TOTAL_ISSUES!"=="JSON_PARSE_ERROR" (
    echo [错误] JSON解析失败！响应可能不是有效的JSON格式
    echo.
    echo 这通常表示：
    echo 1. 认证失败（返回HTML登录页面）
    echo 2. JQL查询语法错误
    echo 3. 服务器返回错误页面
    echo 4. API端点不正确
    echo.
    echo 请检查上面显示的响应内容来确定具体问题

    :: 保留响应文件用于进一步分析
    set "ERROR_FILE=jira_error_response_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "ERROR_FILE=!ERROR_FILE: =0!"
    copy "!TEMP_FILE!" "!ERROR_FILE!" >nul
    echo 错误响应已保存到: !ERROR_FILE!

    del "!TEMP_FILE!"
    pause
    exit /b 1
)

:: 清理临时文件
if exist "!TEMP_FILE!" del "!TEMP_FILE!"

if "!TOTAL_ISSUES!"=="" (
    echo [错误] 无法获取问题数量！
    echo 请检查JQL查询语法是否正确
    pause
    exit /b 1
)

:: 显示结果
echo.
echo ========================================
echo           查询结果
echo ========================================
echo 查询条件: !JQL_QUERY!
echo 问题剩余数量: !TOTAL_ISSUES!
echo 查询时间: %date% %time%
echo ========================================
echo.

:: 可选：将结果保存到文件
set /p SAVE_RESULT="是否将结果保存到文件？(y/n): "
if /i "!SAVE_RESULT!"=="y" (
    set "RESULT_FILE=jira_issues_count_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "RESULT_FILE=!RESULT_FILE: =0!"
    echo 查询时间: %date% %time% > "!RESULT_FILE!"
    echo 查询条件: !JQL_QUERY! >> "!RESULT_FILE!"
    echo 问题剩余数量: !TOTAL_ISSUES! >> "!RESULT_FILE!"
    echo [信息] 结果已保存到: !RESULT_FILE!
)

echo.
echo ========================================
echo 脚本执行完成！
echo.
echo 如果需要修改查询条件，请编辑脚本中的JQL_QUERY变量
echo 当前查询: 状态为"New Analysis"、"Supplier Inbox"或"Rejected"
echo          且未解决、标签包含APA、分配给您的问题
echo ========================================

echo 按任意键退出...
pause >nul
