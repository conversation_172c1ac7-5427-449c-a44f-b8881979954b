@echo off
setlocal enabledelayedexpansion

:: ========================================
:: Jira问题剩余数量获取脚本
:: 作者: Assistant
:: 版本: 1.0
:: 描述: 通过Jira REST API获取问题剩余数量
:: ========================================

echo.
echo ========================================
echo    Jira 问题剩余数量获取工具
echo ========================================
echo.

:: 检查配置文件是否存在
if not exist "jira_config.txt" (
    echo [错误] 配置文件 jira_config.txt 不存在！
    echo 请先创建配置文件，参考 README_jira_script.md
    pause
    exit /b 1
)

:: 读取配置文件
echo [信息] 正在读取配置文件...
for /f "usebackq eol=# tokens=1,* delims==" %%a in ("jira_config.txt") do (
    set "line=%%a"
    if "!line!"=="JIRA_URL" set "JIRA_URL=%%b"
    if "!line!"=="USERNAME" set "USERNAME=%%b"
    if "!line!"=="API_TOKEN" set "API_TOKEN=%%b"
    if "!line!"=="JQL_QUERY" set "JQL_QUERY=%%b"
)

:: 验证必要的配置项
if "!JIRA_URL!"=="" (
    echo [错误] JIRA_URL 未配置！
    pause
    exit /b 1
)
if "!USERNAME!"=="" (
    echo [错误] USERNAME 未配置！
    pause
    exit /b 1
)
if "!API_TOKEN!"=="" (
    echo [错误] API_TOKEN 未配置！
    pause
    exit /b 1
)

:: 如果没有配置JQL查询，使用默认查询
if "!JQL_QUERY!"=="" (
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
    echo [信息] 使用默认JQL查询: !JQL_QUERY!
)

echo [信息] Jira服务器: !JIRA_URL!
echo [信息] 用户名: !USERNAME!
echo [信息] JQL查询: !JQL_QUERY!
echo.

:: 检查curl是否可用
curl --version >nul 2>&1
if errorlevel 1 (
    echo [错误] curl 命令不可用！请确保已安装curl或使用Windows 10/11内置版本
    pause
    exit /b 1
)

:: 构建API请求URL
set "API_URL=!JIRA_URL!/rest/api/2/search"

:: 创建临时文件存储响应
set "TEMP_FILE=%TEMP%\jira_response_%RANDOM%.json"

echo [信息] 正在查询Jira问题...

:: 发送API请求
echo [调试] 请求URL: !API_URL!
echo [调试] 用户名: !USERNAME!
echo [调试] JQL查询: !JQL_QUERY!
echo.

curl -v -u "!USERNAME!:!API_TOKEN!" ^
     -H "Accept: application/json" ^
     -G "!API_URL!" ^
     --data-urlencode "jql=!JQL_QUERY!" ^
     --data-urlencode "maxResults=0" ^
     --data-urlencode "fields=summary" ^
     -o "!TEMP_FILE!" ^
     --connect-timeout 30 ^
     --max-time 60

set CURL_EXIT_CODE=%errorlevel%
echo [调试] curl退出代码: !CURL_EXIT_CODE!

:: 检查curl执行结果
if !CURL_EXIT_CODE! neq 0 (
    echo [错误] API请求失败！curl退出代码: !CURL_EXIT_CODE!
    echo.
    echo 常见错误代码说明:
    echo   6  - 无法解析主机名
    echo   7  - 无法连接到服务器
    echo   22 - HTTP错误（如401未授权、404未找到等）
    echo   28 - 操作超时
    echo   35 - SSL连接错误
    echo.
    echo 请检查:
    echo 1. 网络连接是否正常
    echo 2. Jira URL是否正确
    echo 3. 用户名和API Token是否正确
    echo 4. 防火墙是否阻止了连接

    if exist "!TEMP_FILE!" (
        echo.
        echo [调试] 响应内容:
        type "!TEMP_FILE!"
        del "!TEMP_FILE!"
    )
    pause
    exit /b 1
)

:: 检查响应文件是否存在
if not exist "!TEMP_FILE!" (
    echo [错误] 响应文件未生成！
    pause
    exit /b 1
)

:: 解析JSON响应获取总数
echo [信息] 正在解析响应数据...

:: 使用PowerShell解析JSON（Windows内置）
for /f "delims=" %%i in ('powershell -Command "(Get-Content '!TEMP_FILE!' | ConvertFrom-Json).total"') do (
    set TOTAL_ISSUES=%%i
)

:: 清理临时文件
if exist "!TEMP_FILE!" del "!TEMP_FILE!"

:: 检查是否成功获取数据
if "!TOTAL_ISSUES!"=="" (
    echo [错误] 无法解析响应数据！可能是认证失败或API返回错误
    echo 请检查用户名、API Token和Jira URL是否正确
    pause
    exit /b 1
)

:: 显示结果
echo.
echo ========================================
echo           查询结果
echo ========================================
echo 查询条件: !JQL_QUERY!
echo 问题剩余数量: !TOTAL_ISSUES!
echo 查询时间: %date% %time%
echo ========================================
echo.

:: 可选：将结果保存到文件
set /p SAVE_RESULT="是否将结果保存到文件？(y/n): "
if /i "!SAVE_RESULT!"=="y" (
    set "RESULT_FILE=jira_issues_count_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
    set "RESULT_FILE=!RESULT_FILE: =0!"
    echo 查询时间: %date% %time% > "!RESULT_FILE!"
    echo 查询条件: !JQL_QUERY! >> "!RESULT_FILE!"
    echo 问题剩余数量: !TOTAL_ISSUES! >> "!RESULT_FILE!"
    echo [信息] 结果已保存到: !RESULT_FILE!
)

echo 按任意键退出...
pause >nul
