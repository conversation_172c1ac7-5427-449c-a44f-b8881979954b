@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Jira 配置测试工具
echo ========================================
echo.

:: 检查配置文件是否存在
if not exist "jira_config.txt" (
    echo [错误] 配置文件 jira_config.txt 不存在！
    pause
    exit /b 1
)

echo [信息] 正在读取配置文件...
echo.

:: 读取配置文件并显示
for /f "usebackq eol=# tokens=1,* delims==" %%a in ("jira_config.txt") do (
    set "key=%%a"
    set "value=%%b"
    echo 读取到: [!key!] = [!value!]
    
    if "!key!"=="JIRA_URL" set "JIRA_URL=!value!"
    if "!key!"=="USERNAME" set "USERNAME=!value!"
    if "!key!"=="API_TOKEN" set "API_TOKEN=!value!"
    if "!key!"=="JQL_QUERY" set "JQL_QUERY=!value!"
)

echo.
echo ========================================
echo           配置验证结果
echo ========================================
echo JIRA_URL: [!JIRA_URL!]
echo USERNAME: [!USERNAME!]
echo API_TOKEN: [!API_TOKEN!]
echo JQL_QUERY: [!JQL_QUERY!]
echo ========================================
echo.

:: 测试JQL查询变量
echo [测试] JQL查询变量内容:
echo "!JQL_QUERY!"
echo.

:: 如果没有配置JQL查询，使用默认查询
if "!JQL_QUERY!"=="" (
    set "JQL_QUERY=assignee = currentUser() AND resolution = Unresolved"
    echo [信息] 使用默认JQL查询: !JQL_QUERY!
)

echo [测试] 最终JQL查询:
echo "!JQL_QUERY!"
echo.

echo 按任意键退出...
pause >nul
