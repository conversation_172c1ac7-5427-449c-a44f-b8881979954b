# Perforce 详细使用教程

## 目录
1. [Perforce 简介](#perforce-简介)
2. [安装与配置](#安装与配置)
3. [基础概念](#基础概念)
4. [客户端操作](#客户端操作)
5. [文件操作](#文件操作)
6. [分支管理](#分支管理)
7. [高级功能](#高级功能)
8. [最佳实践](#最佳实践)
9. [常见问题](#常见问题)

---

## Perforce 简介

### 什么是 Perforce？
Perforce（P4）是一个企业级的版本控制系统，特别适用于大型项目和二进制文件管理。它在游戏开发、软件工程等领域广泛使用。

### 主要特点
- **高性能**：处理大文件和大型代码库
- **原子性操作**：确保数据一致性
- **强大的分支合并**：支持复杂的开发流程
- **细粒度权限控制**：企业级安全管理
- **二进制文件支持**：适合游戏资源管理

---

## 安装与配置

### 1. 安装 Perforce 客户端

#### Windows 安装
```bash
# 下载 P4V（可视化客户端）和 P4（命令行客户端）
# 从官网下载：https://www.perforce.com/downloads
```

#### Linux/Mac 安装
```bash
# Ubuntu/Debian
sudo apt-get install perforce-cli

# CentOS/RHEL
sudo yum install perforce-cli

# macOS (使用 Homebrew)
brew install perforce
```

### 2. 基础配置

#### 设置环境变量
```bash
# 设置服务器地址
export P4PORT=ssl:your-server:1666

# 设置用户名
export P4USER=your-username

# 设置客户端工作区名称
export P4CLIENT=your-workspace

# 设置密码（可选）
export P4PASSWD=your-password
```

#### 验证连接
```bash
# 测试服务器连接
p4 info

# 登录服务器
p4 login
```

---

## 基础概念

### 核心概念表

| 概念 | 说明 | 示例 |
|------|------|------|
| **Depot** | 服务器上的文件仓库 | //depot/main/... |
| **Workspace/Client** | 本地工作区 | my-workspace |
| **Changelist** | 变更集合 | CL 12345 |
| **Stream** | 开发流/分支 | //depot/main |
| **Label** | 版本标签 | release-1.0 |
| **Branch** | 分支映射 | main-to-dev |

### 文件状态

| 状态 | 说明 | 操作 |
|------|------|------|
| **Have** | 本地已同步 | p4 sync |
| **Edit** | 已检出编辑 | p4 edit |
| **Add** | 新增文件 | p4 add |
| **Delete** | 删除文件 | p4 delete |
| **Integrate** | 集成/合并 | p4 integrate |

---

## 客户端操作

### 1. 创建工作区

#### 使用 P4V（图形界面）
1. 打开 P4V
2. Connection → New...
3. 填写服务器信息
4. 创建新的 Workspace

#### 使用命令行
```bash
# 创建工作区配置
p4 client my-workspace

# 工作区配置示例
Client: my-workspace
Owner: username
Root: /path/to/local/workspace
Options: noallwrite noclobber nocompress unlocked modtime rmdir
View:
    //depot/main/... //my-workspace/...
```

### 2. 同步文件

```bash
# 同步所有文件到最新版本
p4 sync

# 同步特定路径
p4 sync //depot/main/src/...

# 同步到特定版本
p4 sync //depot/main/...@12345

# 强制同步（覆盖本地修改）
p4 sync -f
```

### 3. 查看文件状态

```bash
# 查看工作区状态
p4 status

# 查看已打开的文件
p4 opened

# 查看文件历史
p4 filelog filename.txt

# 查看文件差异
p4 diff filename.txt
```

---

## 文件操作

### 1. 编辑文件

```bash
# 检出文件进行编辑
p4 edit filename.txt

# 检出多个文件
p4 edit *.cpp

# 检出整个目录
p4 edit src/...
```

### 2. 添加新文件

```bash
# 添加单个文件
p4 add newfile.txt

# 添加多个文件
p4 add *.h

# 递归添加目录
p4 add src/...
```

### 3. 删除文件

```bash
# 删除文件
p4 delete oldfile.txt

# 删除并从本地移除
p4 delete -v oldfile.txt
```

### 4. 移动/重命名文件

```bash
# 移动文件
p4 move oldname.txt newname.txt

# 移动到不同目录
p4 move src/file.cpp include/file.cpp
```

### 5. 提交变更

```bash
# 创建变更列表
p4 change

# 提交默认变更列表
p4 submit

# 提交特定变更列表
p4 submit -c 12345

# 提交时添加描述
p4 submit -d "Fix bug in login system"
```

---

## 分支管理

### 1. 创建分支

```bash
# 创建分支映射
p4 branch my-branch

# 分支配置示例
Branch: my-branch
Owner: username
View:
    //depot/main/... //depot/branches/my-branch/...
```

### 2. 集成操作

```bash
# 从主分支集成到当前分支
p4 integrate //depot/main/... //depot/branches/my-branch/...

# 预览集成
p4 integrate -n //depot/main/... //depot/branches/my-branch/...

# 解决冲突后提交
p4 resolve
p4 submit
```

### 3. 合并分支

```bash
# 将分支合并回主线
p4 integrate //depot/branches/my-branch/... //depot/main/...

# 处理合并冲突
p4 resolve -am  # 自动合并
p4 resolve -as  # 使用源文件
p4 resolve -at  # 使用目标文件
```

---

## 高级功能

### 1. 流（Streams）

```bash
# 创建流
p4 stream -t development //depot/streams/dev

# 切换到流工作区
p4 client -S //depot/streams/dev my-stream-workspace

# 流间合并
p4 merge
p4 copy
```

### 2. 标签管理

```bash
# 创建标签
p4 label release-1.0

# 标记文件
p4 tag -l release-1.0 //depot/main/...@12345

# 同步到标签
p4 sync @release-1.0
```

### 3. 权限管理

```bash
# 查看保护表
p4 protect -o

# 保护表示例
Protections:
    write user * * //depot/...
    admin user admin * //depot/...
    read group developers * //depot/main/...
```

---

## 最佳实践

### 1. 工作流程建议

#### 日常开发流程
```bash
# 1. 同步最新代码
p4 sync

# 2. 检出需要修改的文件
p4 edit file.cpp

# 3. 进行开发工作
# ... 编辑文件 ...

# 4. 查看修改
p4 diff

# 5. 提交变更
p4 submit -d "Implement new feature"
```

#### 分支开发流程
```bash
# 1. 创建功能分支
p4 integrate //depot/main/... //depot/branches/feature-x/...
p4 submit -d "Create feature-x branch"

# 2. 在分支上开发
# ... 开发工作 ...

# 3. 定期从主分支集成
p4 integrate //depot/main/... //depot/branches/feature-x/...
p4 resolve
p4 submit -d "Merge from main"

# 4. 完成后合并回主分支
p4 integrate //depot/branches/feature-x/... //depot/main/...
p4 resolve
p4 submit -d "Merge feature-x to main"
```

### 2. 文件组织建议

```
//depot/
├── main/                    # 主开发分支
│   ├── src/                # 源代码
│   ├── assets/             # 游戏资源
│   ├── docs/               # 文档
│   └── tools/              # 工具脚本
├── branches/               # 功能分支
│   ├── feature-a/
│   └── feature-b/
├── releases/               # 发布版本
│   ├── v1.0/
│   └── v1.1/
└── experimental/           # 实验性代码
```

### 3. 提交信息规范

```bash
# 好的提交信息示例
p4 submit -d "Fix: Resolve memory leak in texture loading"
p4 submit -d "Feature: Add new character animation system"
p4 submit -d "Refactor: Optimize rendering pipeline performance"
p4 submit -d "Docs: Update API documentation for networking module"
```

---

## 常见问题

### 1. 连接问题

**问题**：无法连接到服务器
```bash
# 解决方案
# 检查网络连接
ping your-server

# 检查端口
telnet your-server 1666

# 验证配置
p4 set
```

### 2. 文件冲突

**问题**：集成时出现冲突
```bash
# 解决方案
# 查看冲突文件
p4 resolve -n

# 手动解决冲突
p4 resolve filename.txt

# 使用合并工具
p4 resolve -t filename.txt
```

### 3. 权限问题

**问题**：没有文件操作权限
```bash
# 解决方案
# 检查文件权限
p4 files -m 1 filename.txt

# 联系管理员添加权限
# 或检查保护表设置
p4 protect -o
```

### 4. 工作区问题

**问题**：工作区配置错误
```bash
# 解决方案
# 重新配置工作区
p4 client my-workspace

# 清理工作区
p4 clean

# 重新同步
p4 sync -f
```

---

## 常用命令速查表

| 操作 | 命令 | 说明 |
|------|------|------|
| **连接** | `p4 login` | 登录服务器 |
| **同步** | `p4 sync` | 同步文件 |
| **编辑** | `p4 edit file` | 检出文件 |
| **添加** | `p4 add file` | 添加新文件 |
| **删除** | `p4 delete file` | 删除文件 |
| **提交** | `p4 submit` | 提交变更 |
| **状态** | `p4 status` | 查看状态 |
| **差异** | `p4 diff` | 查看差异 |
| **历史** | `p4 filelog` | 文件历史 |
| **分支** | `p4 integrate` | 分支集成 |

---

## P4V 图形界面操作指南

### 1. P4V 主界面介绍

#### 界面布局
- **Depot View**：服务器文件树视图
- **Workspace View**：本地工作区视图
- **Pending Changes**：待提交的变更
- **History View**：文件历史记录

#### 常用操作
```
右键菜单操作：
├── Get Latest Revision (同步)
├── Check Out for Edit (检出编辑)
├── Add to Source Control (添加到版本控制)
├── Mark for Delete (标记删除)
├── Submit (提交)
└── Revert (撤销)
```

### 2. 可视化分支管理

#### 分支图查看
1. 选择文件 → 右键 → Revision Graph
2. 查看分支关系和合并历史
3. 可视化冲突解决

#### 流程图操作
- **蓝色箭头**：正常集成
- **红色箭头**：冲突需要解决
- **绿色节点**：已解决的合并

---

## 团队协作最佳实践

### 1. 代码审查流程

#### 使用 Swarm 进行代码审查
```bash
# 创建审查请求
p4 review -c 12345

# 命令行提交到审查
p4 submit -r reviewer1,reviewer2
```

#### 审查检查清单
- [ ] 代码符合编码规范
- [ ] 单元测试通过
- [ ] 性能影响评估
- [ ] 安全性检查
- [ ] 文档更新

### 2. 大型项目管理

#### 文件类型配置
```bash
# 设置文件类型
p4 typemap

# 常用类型映射
binary+l    *.exe *.dll *.so
text        *.cpp *.h *.cs
binary+F    *.psd *.max *.fbx
text+ko     *.xml *.json
```

#### 性能优化设置
```bash
# 启用并行同步
p4 configure set net.parallel.max=4

# 设置缓存大小
p4 configure set db.peeking=2

# 启用压缩传输
p4 configure set net.tcpsize=524288
```

---

## 游戏开发特定配置

### 1. Unreal Engine 集成

#### .p4ignore 文件配置
```
# UE4/UE5 忽略文件
Binaries/
DerivedDataCache/
Intermediate/
Saved/
*.tmp
*.log
.vs/
```

#### UE项目 Perforce 设置
```bash
# 设置文件类型
*.uasset binary+l
*.umap binary+l
*.upk binary+l
*.pak binary+l
```

### 2. 大文件管理

#### 使用 Perforce 大文件支持
```bash
# 配置大文件阈值
p4 configure set filesys.bufsize=1M

# 启用增量传输
p4 configure set net.parallel.submit.threads=4
```

---

## 故障排除与维护

### 1. 常见错误解决

#### 错误：文件被锁定
```bash
# 查看谁锁定了文件
p4 opened filename.txt

# 强制解锁（管理员权限）
p4 unlock -f filename.txt
```

#### 错误：工作区不一致
```bash
# 检查工作区一致性
p4 reconcile

# 强制清理工作区
p4 clean -a
```

### 2. 性能监控

#### 服务器性能检查
```bash
# 查看服务器状态
p4 monitor show

# 查看活跃连接
p4 monitor show -a

# 查看长时间运行的命令
p4 monitor show -l
```

---

## 脚本自动化示例

### 1. 自动化构建脚本

```bash
#!/bin/bash
# 自动构建脚本示例

# 同步最新代码
echo "Syncing latest code..."
p4 sync

# 检查是否有未提交的更改
if p4 opened | grep -q .; then
    echo "Warning: You have pending changes"
    p4 opened
    exit 1
fi

# 执行构建
echo "Building project..."
make clean && make

# 如果构建成功，创建标签
if [ $? -eq 0 ]; then
    BUILD_NUMBER=$(date +%Y%m%d_%H%M%S)
    p4 label "build_$BUILD_NUMBER"
    p4 tag -l "build_$BUILD_NUMBER" //depot/main/...
    echo "Build successful, tagged as build_$BUILD_NUMBER"
else
    echo "Build failed"
    exit 1
fi
```

### 2. 批量文件操作脚本

```python
#!/usr/bin/env python3
# 批量文件操作示例

import subprocess
import os

def p4_command(cmd):
    """执行 P4 命令"""
    result = subprocess.run(f"p4 {cmd}", shell=True,
                          capture_output=True, text=True)
    return result.stdout.strip()

def batch_checkout_edit(file_pattern):
    """批量检出文件进行编辑"""
    files = p4_command(f"files {file_pattern}")
    for file_line in files.split('\n'):
        if file_line:
            file_path = file_line.split('#')[0]
            print(f"Checking out: {file_path}")
            p4_command(f"edit {file_path}")

# 使用示例
if __name__ == "__main__":
    batch_checkout_edit("//depot/main/src/*.cpp")
```

---

## 附录

### A. 配置文件模板

#### 客户端配置模板
```
# P4CONFIG 文件内容
P4PORT=ssl:your-server:1666
P4USER=your-username
P4CLIENT=your-workspace
P4CHARSET=utf8
```

#### 类型映射模板
```
# 游戏开发类型映射
binary+l        *.exe *.dll *.so *.dylib
binary+F        *.psd *.max *.ma *.mb *.fbx *.obj
binary+l        *.uasset *.umap *.upk
text+ko         *.xml *.json *.ini *.cfg
text            *.cpp *.h *.cs *.js *.py
binary          *.zip *.rar *.7z *.pak
```

### B. 快捷键参考（P4V）

| 操作 | 快捷键 | 说明 |
|------|--------|------|
| 同步 | Ctrl+S | 同步选中文件 |
| 检出 | Ctrl+E | 检出编辑 |
| 提交 | Ctrl+Enter | 提交变更 |
| 刷新 | F5 | 刷新视图 |
| 搜索 | Ctrl+F | 搜索文件 |
| 历史 | Ctrl+H | 查看历史 |

---

*最后更新：2024年*
*适用版本：Perforce 2023.1+*
*作者：Technical Documentation Team*
