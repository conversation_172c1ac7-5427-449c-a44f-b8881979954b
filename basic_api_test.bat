@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    基础 API 测试工具
echo ========================================
echo.

:: 配置信息
set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"
set "API_TOKEN=NDAYNTM5MDQ5MTIzOru9MRL1pX3mxGCDFWOR8Q8oU762"

echo 配置信息:
echo JIRA_URL: !JIRA_URL!
echo USERNAME: !USERNAME!
echo.

:: 测试1: serverInfo API (不需要认证)
echo [测试1] serverInfo API (无需认证)
set "SERVER_URL=!JIRA_URL!/rest/api/2/serverInfo"
set "TEMP1=%TEMP%\server_info_%RANDOM%.json"

echo 请求URL: !SERVER_URL!
curl -v -s "!SERVER_URL!" -o "!TEMP1!" 2>&1

echo.
echo 响应内容:
if exist "!TEMP1!" (
    type "!TEMP1!"
    del "!TEMP1!"
) else (
    echo 未生成响应文件
)
echo.
echo ========================================
echo.

:: 测试2: myself API (需要认证)
echo [测试2] myself API (需要认证)
set "MYSELF_URL=!JIRA_URL!/rest/api/2/myself"
set "TEMP2=%TEMP%\myself_%RANDOM%.json"

echo 请求URL: !MYSELF_URL!
curl -v -s -u "!USERNAME!:!API_TOKEN!" "!MYSELF_URL!" -o "!TEMP2!" 2>&1

echo.
echo 响应内容:
if exist "!TEMP2!" (
    type "!TEMP2!"
    del "!TEMP2!"
) else (
    echo 未生成响应文件
)
echo.
echo ========================================
echo.

:: 测试3: 搜索API基础测试
echo [测试3] 搜索API基础测试
set "SEARCH_URL=!JIRA_URL!/rest/api/2/search"
set "TEMP3=%TEMP%\search_basic_%RANDOM%.json"

echo 请求URL: !SEARCH_URL!
echo 不使用JQL，只获取基本信息
curl -v -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" "!SEARCH_URL!?maxResults=1" -o "!TEMP3!" 2>&1

echo.
echo 响应内容:
if exist "!TEMP3!" (
    type "!TEMP3!"
    del "!TEMP3!"
) else (
    echo 未生成响应文件
)
echo.
echo ========================================
echo.

:: 测试4: 使用GET方式的简单JQL
echo [测试4] 使用GET方式的简单JQL
set "TEMP4=%TEMP%\search_jql_%RANDOM%.json"

echo 请求URL: !SEARCH_URL!
echo JQL: assignee=currentUser()
curl -v -s -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" "!SEARCH_URL!?jql=assignee=currentUser()&maxResults=1" -o "!TEMP4!" 2>&1

echo.
echo 响应内容:
if exist "!TEMP4!" (
    type "!TEMP4!"
    del "!TEMP4!"
) else (
    echo 未生成响应文件
)
echo.
echo ========================================
echo.

:: 测试5: 检查用户权限
echo [测试5] 检查用户权限和项目访问
set "PROJECTS_URL=!JIRA_URL!/rest/api/2/project"
set "TEMP5=%TEMP%\projects_%RANDOM%.json"

echo 请求URL: !PROJECTS_URL!
curl -v -s -u "!USERNAME!:!API_TOKEN!" "!PROJECTS_URL!" -o "!TEMP5!" 2>&1

echo.
echo 可访问的项目:
if exist "!TEMP5!" (
    powershell -Command "try { $projects = Get-Content '!TEMP5!' | ConvertFrom-Json; $projects | ForEach-Object { Write-Output ('项目: ' + $_.key + ' - ' + $_.name) } } catch { Get-Content '!TEMP5!' }"
    del "!TEMP5!"
) else (
    echo 未获取到项目信息
)

echo.
echo ========================================
echo 测试完成！
echo.
echo 分析说明:
echo 1. 如果测试1失败 - 网络或URL问题
echo 2. 如果测试2失败 - 认证问题
echo 3. 如果测试3失败 - 搜索API权限问题
echo 4. 如果测试4失败 - JQL语法或权限问题
echo 5. 如果测试5失败 - 项目访问权限问题
echo ========================================

pause
