## 提交规范

### 分支命名

- feature/xxx：新功能
- bugfix/xxx：Bug修复
- docs/xxx：文档更新
- refactor/xxx：代码重构

### 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型（type）：

- feat：新功能
- fix：Bug修复
- docs：文档更新
- style：代码格式（不影响代码运行的变动）
- refactor：重构（既不是新增功能，也不是修改bug的代码变动）
- test：增加测试
- chore：构建过程或辅助工具的变动

### 示例

```
feat(client): 添加事件通信重试机制

- 添加指数退避重试策略
- 增加最大重试次数配置
- 添加重试日志记录

Closes #123
```

## 规范

### Header（必填）

#### Type（类型）

| 类型 | 描述 | 示例场景 |
|------|------|----------|
| `feat` | 新功能 | 添加登录功能、实现支付模块 |
| `fix` | Bug修复 | 修复登录失败、解决内存泄漏 |
| `docs` | 文档更新 | 更新README、添加API文档 |
| `style` | 代码格式 | 修复缩进、移除空行 |
| `refactor` | 重构 | 优化算法、重构组件 |
| `perf` | 性能优化 | 提升查询效率、优化渲染 |
| `test` | 测试 | 添加单元测试、修复测试用例 |
| `build` | 构建系统 | 更新webpack配置、修改构建脚本 |
| `ci` | CI/CD | 更新GitHub Actions、修复部署流程 |
| `chore` | 其他 | 更新依赖、删除无用文件 |
| `revert` | 回滚 | 撤销之前的提交 |

#### Scope（范围）

描述影响的模块或组件，可选但推荐：

- `auth`：认证模块
- `api`：API接口
- `ui`：用户界面
- `db`：数据库
- `config`：配置文件
- `deps`：依赖管理

#### Subject（主题）

- 使用祈使句，现在时
- 首字母小写
- 不超过50个字符
- 不以句号结尾

**好的示例：**
- `add user login validation`
- `fix memory leak in event handler`
- `update API documentation`

**避免的写法：**
- `Added user login validation`（过去时）
- `Fix Memory Leak In Event Handler`（大写）
- `update API documentation.`（有句号）

### Body（可选）

- 详细描述为什么做这个改动
- 说明实现方式
- 每行不超过72个字符
- 与header之间空一行

### Footer（可选）

#### Breaking Changes

BREAKING CHANGE: 描述不兼容的变更