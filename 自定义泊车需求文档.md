# 自定义泊车功能需求文档

需求id：837565 v2

需求名：Self selected parking space

## 1. 坐标系定义

### 1.1 屏幕坐标系
- 原点：俯视图区域2D车模的车辆后轴中心
- X轴：水平向上为正方向
- Y轴：水平向右为正方向
- 单位精度：像素(pixels)

### 1.2 自车坐标系
- 与屏幕坐标系一脉相承
- 单位精度：厘米
- 坐标转换：基于2D车模和实际车辆大小的比例关系

### 1.3 视频流坐标系
- 原点：视频流左上角
- X轴：水平向右为正方向
- Y轴：水平向下为正方向

## 2. 自定义车位框功能

### 2.1 初始显示
- 触发条件：收到自定义泊车按键点击信号(APACustomPrkgReq=0x1)
- 显示内容：
  - 自定义车位框
  - 旋转按钮
- 初始位置：
  - 车位框四个点坐标：点1(Px1,Py1)、点2(Px2,Py2)、点3(Px3,Py3)、点4(Px4,Py4)
  - 旋转按钮中心点：点5(Px5,Py5)，位于车位框前侧

### 2.2 交互操作
- 输入信号：PrkgTouchCoornReq（包含坐标和触摸事件类型）
- 触摸事件类型：
  - 0x1/0x2/0x3：用户有屏幕操作
  - 其他：用户无操作

### 2.3 车位框拖动
- 条件：用户在车位框内按下并滑动
- 处理逻辑：
  - 发送信号ScreemOper=0x2
  - 实时判断车位框是否1/3及以上面积在俯视图区域内
  - 满足条件：实时更新位置
  - 不满足条件：固定在最后满足条件的位置

### 2.4 旋转操作
- 条件：用户在旋转按钮上按下并旋转
- 处理逻辑：
  - 实时更新车位框和旋转按钮位置
  - 发送信号ScreemOper=0x2
  - 旋转按钮超出俯视图范围时，自动切换到车位框另一侧

## 3. 车位框状态显示

### 3.1 状态类型
- 可选状态(CustSlotSts==0x1)：效果1
- 可泊状态(CustSlotSts==0x2)：效果2
- 不可泊状态(CustSlotSts==0x3)：效果3
- 隐藏状态(CustSlotSts==0x0)：隐藏车位框

### 3.2 车辆运动时的处理
- 实时计算车位在自车坐标系中的位置
- 转换为屏幕坐标系位置并记录
- 状态从隐藏变为非隐藏时，使用记录的位置显示

## 4. 坐标转换与通信

### 4.1 实时坐标转换
- 条件：车位框状态为非隐藏(CustSlotSts !=0x0)
- 转换内容：屏幕坐标系 → 自车坐标系
- 输出信号：自定义车位四个角点位置
  - (CustPrkSlotPosn.StartX1,StartY1)
  - (CustPrkSlotPosn.StartX2,StartY2)
  - (CustPrkSlotPosn.EndX1,EndY1)
  - (CustPrkSlotPosn.EndX2,EndY2)

## 5. 车位吸附功能

### 5.1 触发条件
- 自定义泊车吸附按键为高亮状态(PrkgCaliBtnSts==0x3)
- 车位框为可选状态(CustSlotSts==0x1)
- 用户未进行拖动或旋转操作
- 接收到的吸附车位位置坐标为非默认值

### 5.2 处理逻辑
- 根据接收到的吸附完成后的车位位置更新屏幕显示
- 使用PrkgCaliSlotPosn信号组中的四个坐标点值

## 6. 注意事项
- 所有UI效果需参考UI设计规范
- 坐标转换需要考虑2D车模和实际车辆的比例关系
- 旋转按钮位置需要实时判断是否在俯视图范围内 