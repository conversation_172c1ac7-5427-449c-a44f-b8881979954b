@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    认证方式对比测试
echo ========================================
echo.

set "JIRA_URL=https://globaljira.geely.com"
set "USERNAME=<EMAIL>"

echo 请输入您的Jira密码:
set /p PASSWORD=

echo.
echo 对比测试开始...
echo.

:: 保存响应到文件进行对比
set "TEMP_SUCCESS=%TEMP%\jira_success_%RANDOM%.txt"
set "TEMP_FAIL=%TEMP%\jira_fail_%RANDOM%.txt"

:: 测试1: 之前成功的方式 (password_auth_test.bat中成功的)
echo [测试1] 重现之前成功的认证方式
echo 命令: curl -v -u "!USERNAME!:!PASSWORD!" "!JIRA_URL!/rest/api/2/myself"
curl -v -u "!USERNAME!:!PASSWORD!" "!JIRA_URL!/rest/api/2/myself" > "!TEMP_SUCCESS!" 2>&1

echo.
echo 成功测试的HTTP状态码:
findstr /i "HTTP/" "!TEMP_SUCCESS!"
echo.

:: 测试2: 脚本中失败的方式 (带更多参数)
echo [测试2] 脚本中使用的方式 (带参数)
echo 命令: curl -s -u "!USERNAME!:!PASSWORD!" -H "Accept: application/json" -G "!JIRA_URL!/rest/api/2/search" --data-urlencode "jql=assignee = currentUser()" --data-urlencode "maxResults=1"
curl -v -u "!USERNAME!:!PASSWORD!" -H "Accept: application/json" -G "!JIRA_URL!/rest/api/2/search" --data-urlencode "jql=assignee = currentUser()" --data-urlencode "maxResults=1" > "!TEMP_FAIL!" 2>&1

echo.
echo 失败测试的HTTP状态码:
findstr /i "HTTP/" "!TEMP_FAIL!"
echo.

:: 对比分析
echo ========================================
echo 详细对比分析:
echo ========================================
echo.

echo [成功的请求详情]:
echo ----------------------------------------
findstr /i "GET\|POST\|HTTP\|Authorization\|User-Agent\|Host" "!TEMP_SUCCESS!"
echo ----------------------------------------
echo.

echo [失败的请求详情]:
echo ----------------------------------------
findstr /i "GET\|POST\|HTTP\|Authorization\|User-Agent\|Host" "!TEMP_FAIL!"
echo ----------------------------------------
echo.

:: 测试3: 简化的搜索请求
echo [测试3] 简化的搜索请求 (不使用-G参数)
curl -v -u "!USERNAME!:!PASSWORD!" -H "Accept: application/json" "!JIRA_URL!/rest/api/2/search?jql=assignee=currentUser()&maxResults=1" 2>&1 | findstr /i "HTTP/"
echo.

:: 测试4: 完全相同的请求到不同端点
echo [测试4] 相同认证方式到搜索端点
curl -v -u "!USERNAME!:!PASSWORD!" "!JIRA_URL!/rest/api/2/search?maxResults=1" 2>&1 | findstr /i "HTTP/"
echo.

:: 清理临时文件
if exist "!TEMP_SUCCESS!" del "!TEMP_SUCCESS!"
if exist "!TEMP_FAIL!" del "!TEMP_FAIL!"

echo ========================================
echo 分析建议:
echo.
echo 1. 如果测试1成功但测试2失败:
echo    - 可能是搜索端点需要特殊权限
echo    - 可能是JQL查询语法问题
echo    - 可能是HTTP方法问题 (-G参数)
echo.
echo 2. 如果所有测试都失败:
echo    - 密码可能在命令行中被转义
echo    - 可能需要特殊的认证头部
echo.
echo 3. 如果测试3或4成功:
echo    - 问题在于curl参数的使用方式
echo ========================================

pause
