# Datasource 插件详细设计文档

## 1. 概述

Datasource 插件是一个用于 Unreal Engine 的数据源管理插件，主要用于在 UE 和 Android/Linux 平台之间进行数据交换和通信。该插件采用发布-订阅模式，允许 UE 蓝图和 C++ 代码与外部平台进行双向数据传输。

## 2. 架构设计

### 2.1 核心组件

插件由以下核心组件构成：

1. **UDatasource**：蓝图函数库类，提供蓝图可调用的接口
2. **UDatasourceImpl**：实现类，负责具体功能实现
3. **Publisher**：发布者类，负责消息的发布和管理
4. **ASubscriber**：订阅者类，负责接收和处理消息
5. **AndroidDataFeeder**：Android 平台数据交互接口

### 2.2 组件关系图

```
+----------------+      +------------------+      +----------------+
|   UDatasource  |----->| UDatasourceImpl  |----->|   Publisher    |
| (蓝图函数库)    |      | (具体实现类)      |      | (消息发布管理) |
+----------------+      +------------------+      +----------------+
                                |                        |
                                v                        v
                        +------------------+      +----------------+
                        |AndroidDataFeeder |<---->|  ASubscriber  |
                        | (Android接口)    |      | (消息订阅处理) |
                        +------------------+      +----------------+
```

## 3. 详细设计

### 3.1 UDatasource 类

UDatasource 是一个蓝图函数库类，提供了一系列静态方法供蓝图调用，是插件的主要入口点。

#### 3.1.1 主要功能

- 创建和启动数据源
- 停止数据源
- 注册订阅者
- 向平台发送各种类型的数据（整数、浮点数、字符串、数组、向量等）
- 设置各种类型的数据值

#### 3.1.2 关键方法

- `CreateAndStartDatasource()`：创建并启动数据源实例
- `StopDatasource()`：停止数据源
- `RegisterSubscriber()`：注册订阅者
- `NotifyPlatformXXX()`：向平台发送各种类型的数据
- `SetXXXValue()`：设置各种类型的数据值

### 3.2 UDatasourceImpl 类

UDatasourceImpl 是实际的实现类，负责具体功能的实现。

#### 3.2.1 主要功能

- 初始化发布者
- 注册和管理订阅者
- 处理数据的发送和接收

#### 3.2.2 关键方法

- `InitPublisher()`：初始化发布者
- `RegisterSub()`：注册订阅者
- `RemoveSub()`：移除订阅者
- `RemoveAllSubs()`：移除所有订阅者
- `NotifyPlatformXXX()`：向平台发送各种类型的数据
- `SetXXXValue()`：设置各种类型的数据值

### 3.3 Publisher 类

Publisher 类负责消息的发布和管理，采用单例模式实现。

#### 3.3.1 主要功能

- 管理订阅者
- 发布消息到订阅者
- 存储消息数据

#### 3.3.2 关键方法

- `GetInstance()`：获取单例实例
- `RegisterSub()`：注册订阅者
- `DelSub()`：删除订阅者
- `DelAllSubs()`：删除所有订阅者
- `PubMsg()`：发布各种类型的消息

#### 3.3.3 数据存储

Publisher 使用多个 unordered_map 存储不同类型的数据：

- `mStoreMapInt`：存储整数类型数据
- `mStoreMapFloat`：存储浮点数类型数据
- `mStoreMapStr`：存储字符串类型数据
- `mStoreMapTarryFloat`：存储浮点数数组类型数据
- `mStoreMapTarryVector`：存储向量数组类型数据
- `mStoreMapTMapVector`：存储向量映射类型数据

### 3.4 ASubscriber 类

ASubscriber 是一个 Actor 类，负责接收和处理消息。

#### 3.4.1 主要功能

- 管理订阅的主题
- 接收和处理各种类型的消息
- 提供蓝图可实现的事件接口

#### 3.4.2 关键方法

- `AddTopic()`：添加订阅主题
- `AddTopics()`：添加多个订阅主题
- `RemoveTopic()`：移除订阅主题
- `RemoveAllTopic()`：移除所有订阅主题
- `IntValueUpdateEvent()`：整数值更新事件
- `FloatValueUpdateEvent()`：浮点数值更新事件
- `FStringValueUpdateEvent()`：字符串值更新事件
- `TarryFloatValueUpdateEvent()`：浮点数数组值更新事件
- `TarryVectorValueUpdateEvent()`：向量数组值更新事件
- `TMapVectorValueUpdateEvent()`：向量映射值更新事件

#### 3.4.3 事件处理

ASubscriber 类定义了大量的事件处理方法，用于处理各种车辆事件类型，如：

- 系统状态变化
- 车辆部件状态（车门、车窗、灯光等）
- 车辆运动状态（速度、角度等）
- AVM（全景影像）相关状态
- APA（自动泊车辅助）相关状态
- HPA（记忆泊车）相关状态
- PDC（泊车雷达）相关状态
- LSDA（车道保持）相关状态

### 3.5 AndroidDataFeeder 类

AndroidDataFeeder 负责与 Android 平台的交互。

#### 3.5.1 主要功能

- 注册数据源
- 处理 JNI 调用
- 在 UE 和 Android 之间传递数据

#### 3.5.2 关键方法

- `registerDatasource()`：注册数据源
- `Java_com_hege_datafeeder_AndroidDataFeeder_SetXXXValue()`：JNI 方法，接收 Android 发送的数据
- `CallbackPlatformXXXValue()`：回调方法，向 Android 发送数据

## 4. 数据流程

### 4.1 UE 到 Android/Linux 的数据流

1. 蓝图或 C++ 代码调用 UDatasource 的 NotifyPlatformXXX 方法
2. UDatasource 将调用转发给 UDatasourceImpl
3. UDatasourceImpl 将数据转换为标准格式并调用 Publisher 的 PubMsg 方法
4. 在编辑器模式下，Publisher 将消息发布给所有订阅者
5. 在设备上运行时，UDatasourceImpl 调用 CallbackPlatformXXXValue 方法
6. CallbackPlatformXXXValue 通过 JNI 将数据发送给 Android

### 4.2 Android/Linux 到 UE 的数据流

1. Android 通过 JNI 调用 Java_com_hege_datafeeder_AndroidDataFeeder_SetXXXValue 方法
2. AndroidDataFeeder 将数据转发给 UDatasourceImpl
3. UDatasourceImpl 调用 Publisher 的 PubMsg 方法
4. Publisher 将消息发布给所有订阅该主题的订阅者
5. 订阅者的相应事件处理方法被调用

## 5. 线程安全

- Publisher 使用 std::atomic<int> mThreadRunning 控制线程状态
- ASubscriber 使用 AsyncTask 确保事件处理在游戏线程中执行

## 6. 内存管理

- UDatasourceImpl 使用 AddToRoot() 防止被垃圾回收
- Publisher 使用单例模式，在 DelAllSubs() 中清理资源
- AndroidDataFeeder 使用 NewGlobalRef 和 ReleaseStringUTFChars 管理 JNI 引用

## 7. 错误处理

- 各个组件在关键操作前进行空指针检查
- JNI 方法在调用前检查环境和对象是否有效
- 日志输出用于调试和错误跟踪
