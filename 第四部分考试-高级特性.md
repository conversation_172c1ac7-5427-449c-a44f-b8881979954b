# 第四部分考试：高级特性（复杂场景）

## 考试说明
- 考试时间：120分钟
- 总分：100分
- 及格分数：70分
- 考试形式：理论题 + 实践操作题 + 综合应用题

---

## 一、选择题（每题2分，共20分）

### 1. Git Submodule主要用于解决什么问题？
A. 代码版本控制
B. 多仓库依赖管理
C. 分支合并冲突
D. 代码审查流程

### 2. 添加Submodule的正确命令是？
A. `git submodule add <url> <path>`
B. `git add submodule <url> <path>`
C. `git module add <url> <path>`
D. `git sub add <url> <path>`

### 3. Git LFS主要用于管理什么类型的文件？
A. 源代码文件
B. 配置文件
C. 大型二进制文件
D. 文档文件

### 4. 启用Git LFS跟踪文件的命令是？
A. `git lfs add "*.psd"`
B. `git lfs track "*.psd"`
C. `git lfs follow "*.psd"`
D. `git lfs watch "*.psd"`

### 5. CI/CD中，MR触发CI的主要目的是？
A. 提高开发效率
B. 自动化部署
C. 确保代码质量
D. 减少人工操作

### 6. 顶层集成仓库的主要职责是？
A. 业务代码开发
B. 功能模块实现
C. 集成管理和CI/CD
D. 文档维护

### 7. 项目里程碑主要用于？
A. 代码版本管理
B. 项目进度跟踪
C. 权限控制
D. 分支管理

### 8. 向顶层集成仓库提交的MR必须关联什么？
A. Issue
B. 里程碑
C. 标签
D. 分支

### 9. Git LFS的存储限制通常是多少？
A. 1GB
B. 5GB
C. 10GB
D. 无限制

### 10. CI配置文件的变更应该通过什么方式提交？
A. 直接推送
B. 邮件申请
C. Merge Request
D. 口头申请

---

## 二、填空题（每空2分，共20分）

### 1. 克隆包含Submodule的项目后，需要执行 ________ 命令来初始化和更新所有子模块。

### 2. Git LFS安装后，需要在项目中执行 ________ 命令来启用LFS功能。

### 3. 顶层集成仓库的CI主要负责 ________、________ 和 ________ 三个方面。

### 4. 里程碑可以分为三种类型：________、________ 和 ________。

### 5. CI配置变更需要经过 ________ 评审，并由 ________ 执行最终合入。

### 6. Git LFS文件下载失败时，可以使用 ________ 命令手动获取所有LFS对象。

### 7. Submodule更新后，需要在主项目中执行 ________ 和 ________ 命令来提交更新。

---

## 三、简答题（每题10分，共30分）

### 1. 详细说明Submodule与Fork工作流结合使用的完整流程，包括各个步骤和注意事项。

### 2. 解释Git LFS的工作原理，说明它如何解决大文件管理问题，并列举适用场景。

### 3. 描述CI/CD集成规范中的分层CI概念，说明项目代码仓库CI和顶层集成CI的区别和职责。

---

## 四、实践操作题（共20分）

### 题目1：Submodule管理实践（10分）

**场景描述：**
你需要为主项目添加一个共享库作为Submodule，并进行更新管理。

**操作要求：**
1. 将仓库 `https://gitlab.com/shared/common-utils.git` 添加为Submodule，路径为 `libs/common-utils`（3分）
2. 提交Submodule添加（2分）
3. 模拟Submodule有更新，进入子模块目录并拉取最新代码（2分）
4. 在主项目中提交Submodule更新（2分）
5. 为其他团队成员提供更新Submodule的命令（1分）

**请写出完整的命令序列：**

```bash
# 在此处写出你的命令
```

### 题目2：Git LFS配置和使用（10分）

**场景描述：**
项目需要管理设计文件和媒体资源，需要配置Git LFS。

**操作要求：**
1. 在项目中启用Git LFS（2分）
2. 配置LFS跟踪以下文件类型：.psd, .ai, .mp4, .zip（3分）
3. 添加一个大文件 `design.psd` 到项目中（2分）
4. 提交LFS配置和文件（2分）
5. 查看当前LFS跟踪的文件（1分）

**请写出完整的命令序列：**

```bash
# 在此处写出你的命令
```

---

## 五、综合应用题（共10分）

### 题目：企业级项目架构设计

**场景描述：**
某公司要开发一个大型电商平台，包含以下模块：
- 用户服务（user-service）
- 商品服务（product-service）
- 订单服务（order-service）
- 支付服务（payment-service）
- 前端应用（frontend-app）
- 移动应用（mobile-app）
- 共享工具库（common-utils）

需要设计完整的Git仓库架构和CI/CD流程。

**设计要求：**

1. **仓库架构设计**（4分）
   - 设计仓库结构和依赖关系
   - 说明Submodule的使用策略
   - 考虑代码复用和模块独立性

2. **CI/CD流程设计**（3分）
   - 设计分层CI策略
   - 说明各层CI的职责和触发条件
   - 考虑集成测试和部署流程

3. **里程碑和发版管理**（3分）
   - 设计里程碑管理策略
   - 制定发版流程和评审机制
   - 考虑多模块协调发版

**请提供详细的设计方案：**

```
在此处编写你的设计方案
```

---

## 六、答案解析

### 选择题答案：
1. B  2. A  3. C  4. B  5. C  6. C  7. B  8. B  9. C  10. C

### 填空题答案：
1. `git submodule update --init --recursive`
2. `git lfs install`
3. 多仓库集成测试、系统级验证、自动化部署
4. 版本里程碑、功能里程碑、阶段里程碑
5. 同行、项目整体负责人
6. `git lfs fetch --all`
7. `git add`、`git commit`

### 简答题参考答案：

**1. Submodule与Fork工作流结合：**

**完整流程：**
1. **项目结构**：主仓库包含多个子模块，每个对应独立功能模块
2. **Fork设置**：开发者从主仓库和子模块仓库分别Fork
3. **开发流程**：
   - 在个人Fork的子模块仓库中开发
   - 通过MR将更改合并到子模块主仓库
   - 更新主项目中的子模块引用
   - 提交主项目更改

**注意事项：**
- 使用HTTPS URL便于访问
- 指定具体分支或标签
- 定期更新子模块
- CI/CD中包含子模块检查

**2. Git LFS工作原理：**

**工作原理：**
- 大文件存储在单独的LFS服务器
- Git仓库中只保留轻量级指针
- 自动处理上传下载过程

**解决问题：**
- 减少仓库大小
- 提高克隆速度
- 改善Git性能

**适用场景：**
- 大型二进制文件（图像、视频、音频）
- 设计文件（PSD、AI）
- 游戏资源文件
- 大型数据集

**3. 分层CI概念：**

**项目代码仓库CI：**
- 职责：单元测试、代码规范、安全扫描、构建验证
- 触发：MR提交时自动触发
- 范围：单个模块的质量检查

**顶层集成CI：**
- 职责：集成测试、系统验证、自动化部署、发布管理
- 触发：顶层仓库MR合并后触发
- 范围：整个系统的集成验证

**区别：**
- 相互独立，互不干扰
- 不同的职责和检查范围
- 不同的触发条件和执行时机

### 实践操作题参考答案：

**题目1：Submodule管理**
```bash
# 1. 添加Submodule
git submodule add https://gitlab.com/shared/common-utils.git libs/common-utils

# 2. 提交Submodule添加
git add .gitmodules libs/common-utils
git commit -m "chore: 添加共享工具库子模块"

# 3. 更新Submodule
cd libs/common-utils
git fetch origin
git checkout main
git pull

# 4. 提交Submodule更新
cd ../../
git add libs/common-utils
git commit -m "chore: 更新共享工具库子模块"

# 5. 团队成员更新命令
git pull
git submodule update --init --recursive
```

**题目2：Git LFS配置**
```bash
# 1. 启用Git LFS
git lfs install

# 2. 配置LFS跟踪
git lfs track "*.psd"
git lfs track "*.ai"
git lfs track "*.mp4"
git lfs track "*.zip"

# 3. 添加大文件
# 创建或复制 design.psd 文件
git add design.psd

# 4. 提交LFS配置和文件
git add .gitattributes
git commit -m "chore: 配置Git LFS并添加设计文件"

# 5. 查看LFS跟踪文件
git lfs track
```

### 综合应用题参考答案：

**1. 仓库架构设计：**
```
顶层集成仓库（ecommerce-platform）
├── user-service (submodule)
├── product-service (submodule)
├── order-service (submodule)
├── payment-service (submodule)
├── frontend-app (submodule)
├── mobile-app (submodule)
└── common-utils (submodule)
```

**Submodule策略：**
- 每个服务独立仓库，便于团队分工
- common-utils作为共享库，被其他服务引用
- 顶层仓库管理版本依赖和集成

**2. CI/CD流程设计：**

**服务层CI：**
- 单元测试、代码规范检查
- 服务独立构建和测试
- Docker镜像构建

**集成层CI：**
- 多服务集成测试
- 端到端测试
- 自动化部署到测试环境
- 生产环境发布

**3. 里程碑和发版管理：**

**里程碑策略：**
- 按功能模块设置里程碑
- 版本发布里程碑
- 重要节点里程碑

**发版流程：**
- 各服务完成开发和测试
- 更新顶层仓库依赖
- 关联里程碑创建MR
- 集成测试和发版评审
- 自动化发布和通知

---

## 评分标准

- **选择题/填空题**：答案准确得满分
- **简答题**：按要点给分，逻辑清晰、内容完整
- **实践操作题**：命令正确、流程完整、格式规范
- **综合应用题**：设计合理、考虑全面、具有可操作性

**扣分项：**
- 命令语法错误：-1分/处
- 流程步骤遗漏：-2分/处
- 设计方案不合理：-3分/处
- 缺乏实际考虑：-2分/处

**加分项：**
- 提出优化建议：+2分
- 考虑安全性和性能：+1分
- 方案具有扩展性：+1分
