# Git LFS 使用手册

Git LFS（Large File Storage）是一个 Git 扩展，用于管理大型文件和二进制文件。它通过将大文件存储在外部服务器上，而不是直接存储在 Git 仓库中，从而优化了仓库的性能。

## 安装 Git LFS

1. **安装 Git LFS**

   在命令行中运行以下命令来安装 Git LFS：

   ```bash
   git lfs install
   ```

2. **验证安装**

   运行以下命令以确保 Git LFS 已正确安装：

   ```bash
   git lfs version
   ```

## 使用 Git LFS

1. **初始化 Git LFS**

   在你的 Git 仓库中初始化 Git LFS：

   ```bash
   git lfs install
   ```

2. **跟踪大文件**

   使用 `git lfs track` 命令来跟踪特定类型的大文件。例如，要跟踪所有的 `.psd` 文件：

   ```bash
   git lfs track "*.psd"
   ```

   这会在仓库中创建或更新 `.gitattributes` 文件。

3. **提交更改**

   在添加和提交文件时，Git LFS 会自动处理被跟踪的文件：

   ```bash
   git add .gitattributes
   git add <your-large-file>
   git commit -m "Add large file with LFS"
   ```

4. **推送到远程仓库**

   使用 `git push` 命令将更改推送到远程仓库。Git LFS 会自动处理大文件的上传：

   ```bash
   git push origin main
   ```

## 常用命令

- **查看被跟踪的文件**

  ```bash
  git lfs ls-files
  ```

- **更新 Git LFS**

  如果需要更新 Git LFS，可以运行：

  ```bash
  git lfs update
  ```

- **卸载 Git LFS**

  如果需要卸载 Git LFS，可以运行：

  ```bash
  git lfs uninstall
  ```

## 注意事项

- 确保你的远程仓库支持 Git LFS。
- 使用 Git LFS 可能会产生额外的存储费用，具体取决于你的托管服务提供商。