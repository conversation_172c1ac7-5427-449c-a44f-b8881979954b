@echo off
chcp 65001 >nul
title 高级鼠标自动点击工具

:menu
cls
echo ========================================
echo       高级鼠标自动点击工具
echo ========================================
echo.
echo 请选择功能：
echo 1. 单点持续点击
echo 2. 多点循环点击
echo 3. 指定坐标点击
echo 4. 退出
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto single_click
if "%choice%"=="2" goto multi_click
if "%choice%"=="3" goto coordinate_click
if "%choice%"=="4" goto exit
goto menu

:single_click
cls
echo ========================================
echo         单点持续点击模式
echo ========================================
echo.
set /p interval="请输入点击间隔（秒，默认为1）: "
if "%interval%"=="" set interval=1

set /p max_clicks="请输入最大点击次数（0为无限制）: "
if "%max_clicks%"=="" set max_clicks=0

echo.
echo 请将鼠标移动到要点击的位置...
pause

powershell -Command "
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

Add-Type -TypeDefinition '
    using System;
    using System.Runtime.InteropServices;
    public class Mouse {
        [DllImport(\"user32.dll\")]
        public static extern void mouse_event(int dwFlags, int dx, int dy, int cButtons, int dwExtraInfo);
        public const int MOUSEEVENTF_LEFTDOWN = 0x02;
        public const int MOUSEEVENTF_LEFTUP = 0x04;
    }
'

$pos = [System.Windows.Forms.Cursor]::Position
Write-Host ('目标位置: X=' + $pos.X + ', Y=' + $pos.Y)
Write-Host '开始自动点击... (按 Ctrl+C 停止)'
Write-Host ''

$count = 0
$maxClicks = %max_clicks%

while ($true) {
    if ($maxClicks -gt 0 -and $count -ge $maxClicks) {
        Write-Host '已达到最大点击次数: ' $maxClicks
        break
    }
    
    try {
        [System.Windows.Forms.Cursor]::Position = New-Object System.Drawing.Point($pos.X, $pos.Y)
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        Start-Sleep -Milliseconds 50
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        
        $count++
        Write-Host ('点击次数: ' + $count + ' - ' + (Get-Date).ToString('HH:mm:ss'))
        
        Start-Sleep -Seconds %interval%
    }
    catch {
        Write-Host '发生错误: ' $_.Exception.Message
        break
    }
}
"
pause
goto menu

:multi_click
cls
echo ========================================
echo         多点循环点击模式
echo ========================================
echo.
set /p point_count="请输入要设置的点击点数量: "
set /p interval="请输入点击间隔（秒，默认为1）: "
if "%interval%"=="" set interval=1

echo.
echo 请依次将鼠标移动到各个点击位置，每个位置按一次回车...

powershell -Command "
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

Add-Type -TypeDefinition '
    using System;
    using System.Runtime.InteropServices;
    public class Mouse {
        [DllImport(\"user32.dll\")]
        public static extern void mouse_event(int dwFlags, int dx, int dy, int cButtons, int dwExtraInfo);
        public const int MOUSEEVENTF_LEFTDOWN = 0x02;
        public const int MOUSEEVENTF_LEFTUP = 0x04;
    }
'

$points = @()
for ($i = 1; $i -le %point_count%; $i++) {
    Write-Host ('请移动鼠标到第 ' + $i + ' 个位置，然后按回车...')
    Read-Host
    $pos = [System.Windows.Forms.Cursor]::Position
    $points += $pos
    Write-Host ('位置 ' + $i + ': X=' + $pos.X + ', Y=' + $pos.Y)
}

Write-Host ''
Write-Host '开始循环点击... (按 Ctrl+C 停止)'
Write-Host ''

$count = 0
while ($true) {
    for ($i = 0; $i -lt $points.Length; $i++) {
        try {
            $pos = $points[$i]
            [System.Windows.Forms.Cursor]::Position = $pos
            [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            Start-Sleep -Milliseconds 50
            [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            $count++
            Write-Host ('点击位置 ' + ($i + 1) + ' (X=' + $pos.X + ', Y=' + $pos.Y + ') - 总次数: ' + $count)
            
            Start-Sleep -Seconds %interval%
        }
        catch {
            Write-Host '发生错误: ' $_.Exception.Message
            break
        }
    }
}
"
pause
goto menu

:coordinate_click
cls
echo ========================================
echo         指定坐标点击模式
echo ========================================
echo.
set /p x_coord="请输入X坐标: "
set /p y_coord="请输入Y坐标: "
set /p interval="请输入点击间隔（秒，默认为1）: "
if "%interval%"=="" set interval=1

powershell -Command "
Add-Type -AssemblyName System.Windows.Forms

Add-Type -TypeDefinition '
    using System;
    using System.Runtime.InteropServices;
    public class Mouse {
        [DllImport(\"user32.dll\")]
        public static extern void mouse_event(int dwFlags, int dx, int dy, int cButtons, int dwExtraInfo);
        public const int MOUSEEVENTF_LEFTDOWN = 0x02;
        public const int MOUSEEVENTF_LEFTUP = 0x04;
    }
'

$x = %x_coord%
$y = %y_coord%

Write-Host ('目标坐标: X=' + $x + ', Y=' + $y)
Write-Host '开始自动点击... (按 Ctrl+C 停止)'
Write-Host ''

$count = 0
while ($true) {
    try {
        [System.Windows.Forms.Cursor]::Position = New-Object System.Drawing.Point($x, $y)
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        Start-Sleep -Milliseconds 50
        [Mouse]::mouse_event([Mouse]::MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        
        $count++
        Write-Host ('点击次数: ' + $count + ' - ' + (Get-Date).ToString('HH:mm:ss'))
        
        Start-Sleep -Seconds %interval%
    }
    catch {
        Write-Host '发生错误: ' $_.Exception.Message
        break
    }
}
"
pause
goto menu

:exit
echo 感谢使用！
exit /b 0
