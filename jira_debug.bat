@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Jira 连接详细诊断工具
echo ========================================
echo.

:: 读取配置
if not exist "jira_config_new.txt" (
    echo [错误] 配置文件 jira_config_new.txt 不存在！
    pause
    exit /b 1
)

for /f "usebackq eol=# tokens=1,* delims==" %%a in ("jira_config_new.txt") do (
    set "line=%%a"
    if "!line!"=="JIRA_URL" set "JIRA_URL=%%b"
    if "!line!"=="USERNAME" set "USERNAME=%%b"
    if "!line!"=="API_TOKEN" set "API_TOKEN=%%b"
    if "!line!"=="JQL_QUERY" set "JQL_QUERY=%%b"
)

echo [配置信息]
echo JIRA_URL: !JIRA_URL!
echo USERNAME: !USERNAME!
echo API_TOKEN: !API_TOKEN:~0,10!...（已隐藏）
echo.

:: 测试1：基本URL访问
echo [测试1] 测试基本URL访问...
set "TEST_FILE=%TEMP%\jira_test_%RANDOM%.html"
curl -v -L --connect-timeout 10 --max-time 30 "!JIRA_URL!" -o "!TEST_FILE!" 2>&1
set "BASIC_CODE=!errorlevel!"
echo 基本访问退出代码: !BASIC_CODE!

if exist "!TEST_FILE!" (
    echo.
    echo [响应内容前200字符]:
    powershell -Command "Get-Content '!TEST_FILE!' -Raw | Select-Object -First 1 | ForEach-Object { $_.Substring(0, [Math]::Min(200, $_.Length)) }"
    del "!TEST_FILE!"
)
echo.

:: 测试2：serverInfo API
echo [测试2] 测试serverInfo API...
set "SERVER_INFO_URL=!JIRA_URL!/rest/api/2/serverInfo"
set "TEST_FILE=%TEMP%\jira_serverinfo_%RANDOM%.json"
curl -v -L --connect-timeout 10 --max-time 30 "!SERVER_INFO_URL!" -o "!TEST_FILE!" 2>&1
set "SERVER_CODE=!errorlevel!"
echo serverInfo API退出代码: !SERVER_CODE!

if exist "!TEST_FILE!" (
    echo.
    echo [serverInfo响应]:
    type "!TEST_FILE!"
    del "!TEST_FILE!"
)
echo.

:: 测试3：认证测试
echo [测试3] 测试认证...
set "AUTH_URL=!JIRA_URL!/rest/api/2/myself"
set "TEST_FILE=%TEMP%\jira_auth_%RANDOM%.json"
curl -v -u "!USERNAME!:!API_TOKEN!" -L --connect-timeout 10 --max-time 30 "!AUTH_URL!" -o "!TEST_FILE!" 2>&1
set "AUTH_CODE=!errorlevel!"
echo 认证测试退出代码: !AUTH_CODE!

if exist "!TEST_FILE!" (
    echo.
    echo [认证响应]:
    type "!TEST_FILE!"
    del "!TEST_FILE!"
)
echo.

:: 测试4：搜索API测试
echo [测试4] 测试搜索API...
set "SEARCH_URL=!JIRA_URL!/rest/api/2/search"
set "TEST_FILE=%TEMP%\jira_search_%RANDOM%.json"
curl -v -u "!USERNAME!:!API_TOKEN!" -H "Accept: application/json" -G "!SEARCH_URL!" --data-urlencode "jql=assignee = currentUser()" --data-urlencode "maxResults=1" -L --connect-timeout 10 --max-time 30 -o "!TEST_FILE!" 2>&1
set "SEARCH_CODE=!errorlevel!"
echo 搜索API退出代码: !SEARCH_CODE!

if exist "!TEST_FILE!" (
    echo.
    echo [搜索响应]:
    type "!TEST_FILE!"
    del "!TEST_FILE!"
)
echo.

:: 网络诊断
echo [网络诊断]
for /f "tokens=3 delims=/" %%a in ("!JIRA_URL!") do set "HOST=%%a"
echo 主机名: !HOST!
echo.

echo 测试ping:
ping -n 2 !HOST!
echo.

echo 测试nslookup:
nslookup !HOST!
echo.

:: 代理检测
echo [代理检测]
echo HTTP_PROXY: %HTTP_PROXY%
echo HTTPS_PROXY: %HTTPS_PROXY%
echo.

echo ========================================
echo 诊断完成！
echo.
echo 根据上述结果分析：
echo 1. 如果基本URL返回HTML登录页面，说明需要认证
echo 2. 如果serverInfo API失败，可能是URL或网络问题
echo 3. 如果认证失败，检查用户名和API Token
echo 4. 如果有代理设置，可能需要配置curl代理
echo ========================================
pause
