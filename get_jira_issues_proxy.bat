@echo off
setlocal enabledelayedexpansion

:: 如果需要代理，请取消注释并配置以下行：
:: set HTTP_PROXY=http://proxy.company.com:8080
:: set HTTPS_PROXY=http://proxy.company.com:8080

:: 或者在curl命令中直接指定代理：
:: --proxy http://proxy.company.com:8080

echo ========================================
echo    Jira 问题剩余数量获取工具（代理版本）
echo ========================================
echo.

:: 检查配置文件
if not exist "jira_config_new.txt" (
    echo [错误] 配置文件 jira_config_new.txt 不存在！
    pause
    exit /b 1
)

:: 读取配置文件
for /f "usebackq eol=# tokens=1,* delims==" %%a in ("jira_config_new.txt") do (
    set "line=%%a"
    if "!line!"=="JIRA_URL" set "JIRA_URL=%%b"
    if "!line!"=="USERNAME" set "USERNAME=%%b"
    if "!line!"=="API_TOKEN" set "API_TOKEN=%%b"
    if "!line!"=="JQL_QUERY" set "JQL_QUERY=%%b"
)

echo [信息] Jira服务器: !JIRA_URL!
echo [信息] 用户名: !USERNAME!
echo.

:: 构建API请求URL
set "API_URL=!JIRA_URL!/rest/api/2/search"
set "TEMP_FILE=%TEMP%\jira_response_%RANDOM%.json"

echo [信息] 正在查询Jira问题...

:: 发送API请求（添加更多调试选项）
curl -v -u "!USERNAME!:!API_TOKEN!" ^
     -H "Accept: application/json" ^
     -H "Content-Type: application/json" ^
     -G "!API_URL!" ^
     --data-urlencode "jql=!JQL_QUERY!" ^
     --data-urlencode "maxResults=0" ^
     --data-urlencode "fields=summary" ^
     -o "!TEMP_FILE!" ^
     --connect-timeout 30 ^
     --max-time 60 ^
     --location ^
     --insecure

set CURL_EXIT_CODE=%errorlevel%
echo [调试] curl退出代码: !CURL_EXIT_CODE!

if !CURL_EXIT_CODE! neq 0 (
    echo [错误] API请求失败！
    if exist "!TEMP_FILE!" (
        echo.
        echo [调试] 响应内容:
        type "!TEMP_FILE!"
        del "!TEMP_FILE!"
    )
    pause
    exit /b 1
)

:: 检查响应内容
if exist "!TEMP_FILE!" (
    echo [调试] 响应文件大小:
    for %%F in ("!TEMP_FILE!") do echo %%~zF 字节
    
    echo.
    echo [调试] 响应内容前500字符:
    powershell -Command "Get-Content '!TEMP_FILE!' -Raw | Select-Object -First 1 | ForEach-Object { $_.Substring(0, [Math]::Min(500, $_.Length)) }"
    
    :: 尝试解析JSON
    for /f "delims=" %%i in ('powershell -Command "try { (Get-Content '!TEMP_FILE!' | ConvertFrom-Json).total } catch { 'JSON_ERROR' }"') do (
        set TOTAL_ISSUES=%%i
    )
    
    del "!TEMP_FILE!"
    
    if "!TOTAL_ISSUES!"=="JSON_ERROR" (
        echo [错误] 响应不是有效的JSON格式，可能是HTML错误页面
    ) else (
        echo.
        echo ========================================
        echo           查询结果
        echo ========================================
        echo 问题剩余数量: !TOTAL_ISSUES!
        echo ========================================
    )
)

pause
