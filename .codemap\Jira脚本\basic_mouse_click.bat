@echo off
title Mouse Auto Click Tool

echo ========================================
echo         Mouse Auto Click Tool
echo ========================================
echo.
echo Instructions:
echo 1. Move mouse to target position
echo 2. Press Enter to start clicking
echo 3. Press Ctrl+C to stop
echo ========================================
echo.

set /p interval="Enter click interval in seconds (default 1): "
if "%interval%"=="" set interval=1

echo.
echo Move mouse to target position and press Enter...
pause >nul

echo.
echo Starting auto click every %interval% seconds...
echo Press Ctrl+C to stop
echo.

set count=0

:loop
set /a count+=1
echo Click %count% at %time%

rem Create temporary VBS file for mouse click
echo CreateObject("WScript.Shell").SendKeys "{LBUTTON}" > temp_click.vbs
cscript //nologo temp_click.vbs
del temp_click.vbs

timeout /t %interval% /nobreak >nul
goto loop
