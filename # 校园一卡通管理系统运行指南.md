# 校园一卡通管理系统运行指南

## 1. 环境准备

确保你的开发环境中安装了以下软件：

- **Java Development Kit (JDK)**: 确保安装了JDK 8或更高版本。
- **Apache Maven**: 用于构建和管理项目依赖。
- **MySQL**: 用于数据库存储。
- **IDE**: 推荐使用 IntelliJ IDEA 或 Eclipse。

## 2. 创建数据库

1. 启动 MySQL 服务。
2. 使用 MySQL 客户端或其他数据库管理工具（如 MySQL Workbench）连接到 MySQL。
3. 执行以下 SQL 语句以创建数据库和表：

   ```sql
   CREATE DATABASE campus_card;

   USE campus_card;

   CREATE TABLE users (
       id INT AUTO_INCREMENT PRIMARY KEY,
       username VARCHAR(50) NOT NULL,
       password VARCHAR(50) NOT NULL,
       role ENUM('student', 'teacher', 'admin') NOT NULL
   );

   CREATE TABLE transactions (
       id INT AUTO_INCREMENT PRIMARY KEY,
       user_id INT,
       amount DECIMAL(10, 2),
       transaction_type ENUM('recharge', 'consume'),
       transaction_time DATETIME DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (user_id) REFERENCES users(id)
   );
   ```

## 3. 创建 Maven 项目

1. 在你的 IDE 中创建一个新的 Maven 项目。
2. 将以下 `pom.xml` 文件内容复制到你的项目中：

   ```xml
   <project xmlns="http://maven.apache.org/POM/4.0.0"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
       <modelVersion>4.0.0</modelVersion>
       <groupId>com.example</groupId>
       <artifactId>campus-card-management</artifactId>
       <version>1.0-SNAPSHOT</version>

       <dependencies>
           <dependency>
               <groupId>org.springframework</groupId>
               <artifactId>spring-webmvc</artifactId>
               <version>5.3.10</version>
           </dependency>
           <dependency>
               <groupId>org.mybatis</groupId>
               <artifactId>mybatis-spring</artifactId>
               <version>2.0.6</version>
           </dependency>
           <dependency>
               <groupId>mysql</groupId>
               <artifactId>mysql-connector-java</artifactId>
               <version>8.0.26</version>
           </dependency>
           <dependency>
               <groupId>javax.servlet</groupId>
               <artifactId>javax.servlet-api</artifactId>
               <version>4.0.1</version>
               <scope>provided</scope>
           </dependency>
           <dependency>
               <groupId>org.springframework</groupId>
               <artifactId>spring-context</artifactId>
               <version>5.3.10</version>
           </dependency>
       </dependencies>
   </project>
   ```

## 4. 配置 Spring 和 MyBatis

1. 在 `src/main/resources` 目录下创建 `applicationContext.xml` 和 `mybatis-config.xml` 文件，并将以下内容复制到相应文件中。

### `applicationContext.xml`

   ```xml
   <beans xmlns="http://www.springframework.org/schema/beans"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://www.springframework.org/schema/beans
                              http://www.springframework.org/schema/beans/spring-beans.xsd">

       <bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource">
           <property name="driverClassName" value="com.mysql.cj.jdbc.Driver"/>
           <property name="url" value="***************************************"/>
           <property name="username" value="your_username"/>
           <property name="password" value="your_password"/>
       </bean>

       <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
           <property name="dataSource" ref="dataSource"/>
       </bean>

       <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
           <property name="dataSource" ref="dataSource"/>
       </bean>

       <bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
           <constructor-arg index="0" ref="sqlSessionFactory"/>
       </bean>

       <context:component-scan base-package="com.example"/>
   </beans>
   ```

### `mybatis-config.xml`

   ```xml
   <configuration>
       <settings>
           <setting name="mapUnderscoreToCamelCase" value="true"/>
       </settings>
   </configuration>
   ```

## 5. 编写代码

将以下 Java 代码（模型、映射器、服务、控制器等）复制到相应的 Java 文件中。

### 用户模型（`User.java`）

   ```java
   package com.example.model;

   public class User {
       private int id;
       private String username;
       private String password;
       private String role;

       // Getters and Setters
   }
   ```

### 用户映射器（`UserMapper.java`）

   ```java
   package com.example.mapper;

   import com.example.model.User;
   import org.apache.ibatis.annotations.Insert;
   import org.apache.ibatis.annotations.Select;

   public interface UserMapper {
       @Insert("INSERT INTO users (username, password, role) VALUES (#{username}, #{password}, #{role})")
       void register(User user);

       @Select("SELECT * FROM users WHERE id = #{id}")
       User getUserById(int id);
   }
   ```

### 用户服务（`UserService.java`）

   ```java
   package com.example.service;

   import com.example.mapper.UserMapper;
   import com.example.model.User;
   import org.springframework.beans.factory.annotation.Autowired;
   import org.springframework.stereotype.Service;

   @Service
   public class UserService {
       @Autowired
       private UserMapper userMapper;

       public void register(User user) {
           userMapper.register(user);
       }

       public User getUserById(int id) {
           return userMapper.getUserById(id);
       }
   }
   ```

### 用户控制器（`UserController.java`）

   ```java
   package com.example.controller;

   import com.example.model.User;
   import com.example.service.UserService;
   import org.springframework.beans.factory.annotation.Autowired;
   import org.springframework.web.bind.annotation.*;

   @RestController
   @RequestMapping("/user")
   public class UserController {
       @Autowired
       private UserService userService;

       @PostMapping("/register")
       public String register(@RequestBody User user) {
           userService.register(user);
           return "注册成功";
       }

       @GetMapping("/{id}")
       public User getUser(@PathVariable int id) {
           return userService.getUserById(id);
       }
   }
   ```

## 6. 配置 Web.xml

在 `src/main/webapp/WEB-INF` 目录下创建 `web.xml` 文件，并将以下内容复制到该文件中。

```xml
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
                             http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
         version="3.1">

    <servlet>
        <servlet-name>dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>dispatcher</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>/WEB-INF/applicationContext.xml</param-value>
    </context-param>

    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
</web-app>
```

## 7. 启动 Tomcat 服务器

1. 下载并安装 Apache Tomcat（建议使用 Tomcat 9 或更高版本）。
2. 将你的 Maven 项目构建为 WAR 文件：
   - 在命令行中，导航到项目根目录并运行以下命令：
     ```bash
     mvn clean package
     ```
   - 这将在 `target` 目录下生成一个 WAR 文件。
3. 将生成的 WAR 文件复制到 Tomcat 的 `webapps` 目录中。
4. 启动 Tomcat 服务器：
   - 在 Tomcat 的 `bin` 目录下，运行 `startup.bat`（Windows）或 `startup.sh`（Linux/Mac）。

## 8. 访问应用程序

1. 打开浏览器，访问以下 URL：
   ```
   http://localhost:8080/your-war-file-name/
   ```
   其中 `your-war-file-name` 是你生成的 WAR 文件的名称（不包括 `.war` 后缀）。
   
2. 你应该能够看到校园一卡通管理系统的前端页面。

## 9. 测试功能

- 在注册表单中输入用户名和密码，点击注册按钮。
- 检查 MySQL 数据库中的 `users` 表，确认用户是否成功注册。

## 10. 调试和开发

- 如果在运行过程中遇到问题，请查看 Tomcat 的日志文件（通常在 `logs` 目录下），以获取错误信息。
- 根据需要修改代码并重新构建项目。

---

以上是校园一卡通管理系统的运行指南。如果你在某个步骤中遇到问题，或者有其他具体问题，请随时告诉我！