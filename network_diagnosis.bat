@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    网络连接诊断工具
echo ========================================
echo.

set "JIRA_HOST=globaljira.geely.com"

echo [测试1] DNS解析测试
echo 测试域名: %JIRA_HOST%
echo.

echo nslookup结果:
nslookup %JIRA_HOST%
echo.

echo [测试2] ping测试
ping -n 4 %JIRA_HOST%
echo.

echo [测试3] 网络配置检查
echo.
echo 当前DNS服务器:
ipconfig /all | findstr "DNS"
echo.

echo 网络适配器信息:
ipconfig | findstr "适配器\|IPv4\|子网掩码\|默认网关"
echo.

echo [测试4] 路由追踪
echo tracert %JIRA_HOST% (前5跳):
tracert -h 5 %JIRA_HOST%
echo.

echo [测试5] 检查hosts文件
echo.
echo hosts文件中的相关条目:
findstr /i "geely\|jira" C:\Windows\System32\drivers\etc\hosts
if errorlevel 1 echo 未找到相关条目
echo.

echo [测试6] 公网连接测试
echo 测试公网连接 (ping google.com):
ping -n 2 google.com
echo.

echo [测试7] 公司域名测试
echo 测试geely.com主域名:
nslookup geely.com
echo.

echo ========================================
echo 诊断建议:
echo.
echo 1. 如果DNS解析失败:
echo    - 检查是否连接了公司VPN
echo    - 确认DNS服务器设置
echo    - 联系IT部门确认域名
echo.
echo 2. 如果ping失败但DNS解析成功:
echo    - 可能是防火墙阻止ping
echo    - 尝试使用telnet测试端口
echo.
echo 3. 如果完全无法访问:
echo    - 确认您是否在公司网络内
echo    - 检查VPN连接状态
echo    - 确认Jira服务器地址是否正确
echo ========================================

pause
