# 第一部分考试：Git基础（个人开发）

## 考试说明
- 考试时间：60分钟
- 总分：100分
- 及格分数：70分
- 考试形式：理论题 + 实践操作题

---

## 一、选择题（每题2分，共20分）

### 1. 以下哪个命令用于查看所有分支（包括远程分支）？
A. `git branch`
B. `git branch -r`
C. `git branch -a`
D. `git branch --all`

### 2. 创建并切换到新分支的正确命令是？
A. `git branch feature/login && git checkout feature/login`
B. `git checkout -b feature/login`
C. `git create feature/login`
D. `git switch -c feature/login`

### 3. 以下哪个分支命名是正确的？
A. `Feature/User-Login`
B. `feature/123-user-login`
C. `feature/User_Login`
D. `FEATURE/user/login`

### 4. 提交消息中，表示新功能的类型标识是？
A. `new`
B. `feature`
C. `feat`
D. `add`

### 5. 以下哪个命令用于查看暂存区的差异？
A. `git diff`
B. `git diff --cached`
C. `git diff HEAD`
D. `git status`

### 6. 修改最后一次提交的命令是？
A. `git commit --modify`
B. `git commit --amend`
C. `git commit --edit`
D. `git commit --update`

### 7. 以下哪个提交消息格式是正确的？
A. `Fix: 修复登录bug`
B. `fix: 修复登录bug`
C. `FIX: 修复登录bug`
D. `修复登录bug`

### 8. 查看提交历史的简洁格式命令是？
A. `git log`
B. `git log --oneline`
C. `git log --short`
D. `git log --brief`

### 9. 删除远程分支的正确命令是？
A. `git branch -d origin/feature/test`
B. `git push origin --delete feature/test`
C. `git remote delete feature/test`
D. `git delete origin feature/test`

### 10. 以下哪个范围（scope）在提交消息中是合适的？
A. `feat(用户模块): 添加登录功能`
B. `feat(user): 添加登录功能`
C. `feat(USER): 添加登录功能`
D. `feat(user-module): 添加登录功能`

---

## 二、填空题（每空2分，共20分）

### 1. Git分支命名规范中，功能分支的前缀是 ________，热修复分支的前缀是 ________。

### 2. 提交消息的完整格式包含三个部分：________ 、________ 和 ________。

### 3. 查看特定文件修改历史的命令是 `git log ________ filename`。

### 4. 将所有修改添加到暂存区的命令是 ________。

### 5. 语义化版本号格式为：________.________.________。

### 6. 提交消息主题部分应使用 ________ 句，________ 时，首字母 ________。

### 7. 获取远程仓库最新更新但不合并的命令是 ________。

---

## 三、简答题（每题10分，共30分）

### 1. 请说明Git Flow中长期分支和临时分支的区别，并列举各自包含哪些分支类型？

### 2. 解释提交消息规范中以下类型的含义和使用场景：
   - feat
   - fix
   - docs
   - refactor
   - chore

### 3. 描述分支命名规范的要求，并给出5个符合规范的分支名称示例。

---

## 四、实践操作题（共30分）

### 题目1：基础Git操作（15分）

**场景描述：**
你需要为一个项目创建新功能分支并进行开发。

**操作要求：**
1. 创建并切换到名为 `feature/456-user-profile` 的新分支（3分）
2. 创建一个名为 `profile.js` 的文件，内容为 `console.log('用户资料页面');`（2分）
3. 将文件添加到暂存区（2分）
4. 提交更改，提交消息为 `feat(user): 添加用户资料页面`（3分）
5. 查看提交历史（简洁格式）（2分）
6. 查看当前分支状态（2分）
7. 推送分支到远程仓库（1分）

**请写出完整的命令序列：**

```bash
# 在此处写出你的命令
```

### 题目2：分支管理和提交规范（15分）

**场景描述：**
你需要修复一个紧急bug，然后进行代码重构。

**操作要求：**
1. 从main分支创建热修复分支 `hotfix/789-login-error`（3分）
2. 修改 `login.js` 文件（模拟修复bug）（2分）
3. 提交修复，使用正确的提交消息格式（3分）
4. 切换回main分支并创建重构分支 `refactor/optimize-auth`（3分）
5. 修改 `auth.js` 文件（模拟重构）（2分）
6. 提交重构，使用正确的提交消息格式（2分）

**请写出完整的命令序列和提交消息：**

```bash
# 在此处写出你的命令和提交消息
```

---

## 五、答案解析

### 选择题答案：
1. C  2. B  3. B  4. C  5. B  6. B  7. B  8. B  9. B  10. B

### 填空题答案：
1. feature/，hotfix/
2. 类型（Type）、正文（Body）、页脚（Footer）
3. -p
4. git add .
5. 主版本号，次版本号，修订号
6. 祈使，现在，小写
7. git fetch

### 简答题参考答案：
1. **长期分支**：长期存在，不会被删除
   - master/main：生产环境代码
   - develop：开发环境代码
   
   **临时分支**：完成特定任务后会被删除
   - feature：功能开发
   - bugfix：bug修复
   - hotfix：紧急修复
   - release：版本发布
   - docs：文档更新
   - refactor：代码重构

2. **提交类型说明：**
   - feat：新功能开发
   - fix：bug修复
   - docs：文档更新
   - refactor：代码重构，不改变功能
   - chore：构建过程或辅助工具的变动

3. **分支命名规范：**
   - 使用小写字母
   - 单词间用连字符分隔
   - 包含前缀和任务编号
   - 简短描述不超过50字符
   
   **示例：**
   - feature/123-user-login
   - bugfix/456-fix-memory-leak
   - hotfix/789-security-patch
   - docs/update-readme
   - refactor/optimize-queries

### 实践操作题参考答案：

**题目1：**
```bash
git checkout -b feature/456-user-profile
echo "console.log('用户资料页面');" > profile.js
git add profile.js
git commit -m "feat(user): 添加用户资料页面"
git log --oneline
git status
git push origin feature/456-user-profile
```

**题目2：**
```bash
git checkout main
git checkout -b hotfix/789-login-error
# 编辑 login.js 文件
git add login.js
git commit -m "fix(auth): 修复登录错误问题"
git checkout main
git checkout -b refactor/optimize-auth
# 编辑 auth.js 文件
git add auth.js
git commit -m "refactor(auth): 优化认证模块代码结构"
```

---

## 评分标准

- **选择题**：每题2分，答案正确得满分
- **填空题**：每空2分，答案准确得满分
- **简答题**：按要点给分，表述清晰、要点完整得满分
- **实践操作题**：按步骤给分，命令正确、格式规范得满分

**注意事项：**
- 命令拼写错误扣1分
- 提交消息格式不规范扣2分
- 分支命名不符合规范扣2分
